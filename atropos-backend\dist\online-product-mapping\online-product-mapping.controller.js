"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineProductMappingController = void 0;
const common_1 = require("@nestjs/common");
const online_product_mapping_service_1 = require("./online-product-mapping.service");
const create_online_product_mapping_dto_1 = require("./dto/create-online-product-mapping.dto");
const update_online_product_mapping_dto_1 = require("./dto/update-online-product-mapping.dto");
let OnlineProductMappingController = class OnlineProductMappingController {
    onlineProductMappingService;
    constructor(onlineProductMappingService) {
        this.onlineProductMappingService = onlineProductMappingService;
    }
    create(createOnlineProductMappingDto) {
        return this.onlineProductMappingService.createOnlineProductMapping(createOnlineProductMappingDto);
    }
    findAll(platformId, productId) {
        return this.onlineProductMappingService.findAllOnlineProductMappings(platformId, productId);
    }
    findOne(id) {
        return this.onlineProductMappingService.findOneOnlineProductMapping(id);
    }
    update(id, updateOnlineProductMappingDto) {
        return this.onlineProductMappingService.updateOnlineProductMapping(id, updateOnlineProductMappingDto);
    }
    remove(id) {
        return this.onlineProductMappingService.removeOnlineProductMapping(id);
    }
};
exports.OnlineProductMappingController = OnlineProductMappingController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_online_product_mapping_dto_1.CreateOnlineProductMappingDto]),
    __metadata("design:returntype", void 0)
], OnlineProductMappingController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('platformId')),
    __param(1, (0, common_1.Query)('productId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], OnlineProductMappingController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlineProductMappingController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_online_product_mapping_dto_1.UpdateOnlineProductMappingDto]),
    __metadata("design:returntype", void 0)
], OnlineProductMappingController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlineProductMappingController.prototype, "remove", null);
exports.OnlineProductMappingController = OnlineProductMappingController = __decorate([
    (0, common_1.Controller)('online-product-mapping'),
    __metadata("design:paramtypes", [online_product_mapping_service_1.OnlineProductMappingService])
], OnlineProductMappingController);
//# sourceMappingURL=online-product-mapping.controller.js.map