import { PrismaService } from '../prisma/prisma.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { $Enums } from '../../generated/prisma';
export declare class TaskService {
    private prisma;
    constructor(prisma: PrismaService);
    createTask(data: CreateTaskDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
    findAllTasks(companyId?: string, branchId?: string, assignedToId?: string, status?: $Enums.TaskStatus, priority?: $Enums.TaskPriority, startDate?: Date, endDate?: Date): Promise<({
        company: {
            name: string;
            id: string;
        };
        branch: {
            name: string;
            id: string;
        } | null;
        assignedTo: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        createdByUser: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    })[]>;
    findOneTask(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
        branch: {
            name: string;
            id: string;
        } | null;
        assignedTo: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        createdByUser: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
    updateTask(id: string, data: UpdateTaskDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
    removeTask(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
}
