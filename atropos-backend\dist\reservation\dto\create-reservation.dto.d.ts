import { ReservationStatus, ReservationSource } from '../../../generated/prisma';
export declare class CreateReservationDto {
    branchId: string;
    customerId?: string;
    customerName: string;
    phone: string;
    customerEmail?: string;
    reservationDate: Date;
    reservationTime: string;
    duration?: number;
    guestCount: number;
    childCount?: number;
    tableIds?: string[];
    tablePreference?: string;
    status?: ReservationStatus;
    specialRequests?: string;
    allergyInfo?: string;
    occasionType?: string;
    internalNotes?: string;
    source?: ReservationSource;
    confirmationCode?: string;
    confirmedBy?: string;
    depositRequired?: boolean;
    depositAmount?: number;
    depositPaid?: boolean;
    cancelReason?: string;
    noShowFee?: number;
    createdBy?: string;
}
