import { InventoryItemService } from './inventory-item.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';
export declare class InventoryItemController {
    private readonly inventoryItemService;
    constructor(inventoryItemService: InventoryItemService);
    create(createInventoryItemDto: CreateInventoryItemDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
    findAll(productId?: string, code?: string, supplier?: string): Promise<({
        product: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    })[]>;
    findOne(id: string): Promise<{
        product: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
    update(id: string, updateInventoryItemDto: UpdateInventoryItemDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
}
