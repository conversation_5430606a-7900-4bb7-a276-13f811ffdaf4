declare enum OnlineOrderStatus {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED",
    PREPARING = "PREPARING",
    READY = "READY",
    DELIVERING = "DELIVERING",
    DELIVERED = "DELIVERED",
    CANCELLED = "CANCELLED",
    RETURNED = "RETURNED"
}
export declare class UpdateOnlineOrderDto {
    status?: OnlineOrderStatus;
    platformStatus?: string;
    acceptedAt?: Date;
    rejectedAt?: Date;
    preparingAt?: Date;
    readyAt?: Date;
    deliveringAt?: Date;
    deliveredAt?: Date;
    cancelledAt?: Date;
    rejectReason?: string;
    cancelReason?: string;
    isPaid?: boolean;
    orderData?: any;
}
export {};
