'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const react$1 = require('@zag-js/react');
const react = require('react');
const factory = require('../factory.cjs');
const useFloatingPanelContext = require('./use-floating-panel-context.cjs');

const FloatingPanelBody = react.forwardRef((props, ref) => {
  const floatingPanel = useFloatingPanelContext.useFloatingPanelContext();
  const mergedProps = react$1.mergeProps(floatingPanel.getBodyProps(), props);
  return /* @__PURE__ */ jsxRuntime.jsx(factory.ark.div, { ...mergedProps, ref });
});
FloatingPanelBody.displayName = "FloatingPanelBody";

exports.FloatingPanelBody = FloatingPanelBody;
