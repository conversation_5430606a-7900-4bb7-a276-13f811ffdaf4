"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockCountController = void 0;
const common_1 = require("@nestjs/common");
const stock_count_service_1 = require("./stock-count.service");
const create_stock_count_dto_1 = require("./dto/create-stock-count.dto");
const update_stock_count_dto_1 = require("./dto/update-stock-count.dto");
const prisma_1 = require("../../generated/prisma");
let StockCountController = class StockCountController {
    stockCountService;
    constructor(stockCountService) {
        this.stockCountService = stockCountService;
    }
    create(createStockCountDto) {
        return this.stockCountService.createStockCount(createStockCountDto);
    }
    findAll(branchId, countType, status, startDate, endDate) {
        return this.stockCountService.findAllStockCounts(branchId, countType, status, startDate ? new Date(startDate) : undefined, endDate ? new Date(endDate) : undefined);
    }
    findOne(id) {
        return this.stockCountService.findOneStockCount(id);
    }
    update(id, updateStockCountDto) {
        return this.stockCountService.updateStockCount(id, updateStockCountDto);
    }
    remove(id) {
        return this.stockCountService.removeStockCount(id);
    }
};
exports.StockCountController = StockCountController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_stock_count_dto_1.CreateStockCountDto]),
    __metadata("design:returntype", void 0)
], StockCountController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('branchId')),
    __param(1, (0, common_1.Query)('countType')),
    __param(2, (0, common_1.Query)('status')),
    __param(3, (0, common_1.Query)('startDate')),
    __param(4, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", void 0)
], StockCountController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StockCountController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_stock_count_dto_1.UpdateStockCountDto]),
    __metadata("design:returntype", void 0)
], StockCountController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StockCountController.prototype, "remove", null);
exports.StockCountController = StockCountController = __decorate([
    (0, common_1.Controller)('stock-count'),
    __metadata("design:paramtypes", [stock_count_service_1.StockCountService])
], StockCountController);
//# sourceMappingURL=stock-count.controller.js.map