"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const library_1 = require("@prisma/client/runtime/library");
let PrismaExceptionFilter = class PrismaExceptionFilter {
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'An unexpected error occurred.';
        let error = 'Internal Server Error';
        switch (exception.code) {
            case 'P2000':
                status = common_1.HttpStatus.BAD_REQUEST;
                message = `The value for column '${exception.meta?.target || 'unknown'}' is too long.`;
                error = 'Bad Request';
                break;
            case 'P2002':
                status = common_1.HttpStatus.CONFLICT;
                message = `Duplicate entry for unique field: ${exception.meta?.target}.`;
                error = 'Conflict';
                break;
            case 'P2025':
                status = common_1.HttpStatus.NOT_FOUND;
                message = `Record not found. ${exception.meta?.cause || ''}`;
                error = 'Not Found';
                break;
            case 'P2003':
                status = common_1.HttpStatus.BAD_REQUEST;
                message = `Foreign key constraint failed on the field: ${exception.meta?.field_name || 'unknown'}. This record is referenced by another entity.`;
                error = 'Bad Request';
                break;
            default:
                console.error('Unhandled Prisma error:', exception);
                message = 'An unexpected database error occurred.';
                break;
        }
        response.status(status).json({
            statusCode: status,
            message: message,
            error: error,
            timestamp: new Date().toISOString(),
            path: request.url,
        });
    }
};
exports.PrismaExceptionFilter = PrismaExceptionFilter;
exports.PrismaExceptionFilter = PrismaExceptionFilter = __decorate([
    (0, common_1.Catch)(library_1.PrismaClientKnownRequestError)
], PrismaExceptionFilter);
//# sourceMappingURL=prisma-exception.filter.js.map