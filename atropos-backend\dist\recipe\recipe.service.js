"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let RecipeService = class RecipeService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createRecipe(data) {
        const productExists = await this.prisma.product.findUnique({
            where: { id: data.productId, deletedAt: null },
        });
        if (!productExists) {
            throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
        }
        const existingRecipe = await this.prisma.recipe.findUnique({
            where: { productId: data.productId },
        });
        if (existingRecipe) {
            throw new common_1.ConflictException(`Recipe already exists for product with ID "${data.productId}".`);
        }
        for (const item of data.items) {
            const inventoryItem = await this.prisma.inventoryItem.findUnique({
                where: { id: item.inventoryItemId, deletedAt: null },
            });
            if (!inventoryItem) {
                throw new common_1.NotFoundException(`Inventory item with ID "${item.inventoryItemId}" not found.`);
            }
            if (inventoryItem.unit !== item.unit) {
                throw new common_1.BadRequestException(`Unit mismatch: Inventory item "${inventoryItem.name}" is in ${inventoryItem.unit}, but recipe item uses ${item.unit}.`);
            }
        }
        const recipe = await this.prisma.recipe.create({
            data: {
                ...data,
                yield: parseFloat(data.yield.toFixed(3)),
                items: {
                    create: data.items.map(item => ({
                        inventoryItemId: item.inventoryItemId,
                        quantity: parseFloat(item.quantity.toFixed(3)),
                        unit: item.unit,
                        wastagePercent: item.wastagePercent !== undefined ? parseFloat(item.wastagePercent.toFixed(2)) : undefined,
                    })),
                },
            },
            include: { items: true },
        });
        return recipe;
    }
    async findAllRecipes(productId) {
        return this.prisma.recipe.findMany({
            where: {
                productId: productId || undefined,
                deletedAt: null,
            },
            include: {
                product: { select: { id: true, name: true, code: true } },
                items: { include: { inventoryItem: { select: { id: true, name: true, unit: true } } } },
            },
            orderBy: { name: 'asc' },
        });
    }
    async findOneRecipe(id) {
        const recipe = await this.prisma.recipe.findUnique({
            where: { id, deletedAt: null },
            include: {
                product: { select: { id: true, name: true, code: true } },
                items: { include: { inventoryItem: { select: { id: true, name: true, unit: true } } } },
            },
        });
        if (!recipe) {
            throw new common_1.NotFoundException(`Recipe with ID "${id}" not found.`);
        }
        return recipe;
    }
    async updateRecipe(id, data) {
        const existingRecipe = await this.findOneRecipe(id);
        if (data.productId && data.productId !== existingRecipe.productId) {
            const productExists = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
            if (!productExists) {
                throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
            }
            const existingRecipeForNewProduct = await this.prisma.recipe.findUnique({ where: { productId: data.productId } });
            if (existingRecipeForNewProduct && existingRecipeForNewProduct.id !== id) {
                throw new common_1.ConflictException(`Recipe already exists for product with ID "${data.productId}".`);
            }
        }
        const transaction = [];
        if (data.items) {
            const currentRecipeItems = await this.prisma.recipeItem.findMany({ where: { recipeId: id } });
            const currentItemIds = new Set(currentRecipeItems.map(item => item.id));
            const incomingItemIds = new Set(data.items.filter(item => item.id).map(item => item.id));
            const itemsToDelete = currentRecipeItems.filter(item => !incomingItemIds.has(item.id));
            if (itemsToDelete.length > 0) {
                transaction.push(this.prisma.recipeItem.deleteMany({
                    where: { id: { in: itemsToDelete.map(item => item.id) } }
                }));
            }
            for (const item of data.items) {
                if (item.id && currentItemIds.has(item.id)) {
                    const updateData = {};
                    if (item.inventoryItemId)
                        updateData.inventoryItemId = item.inventoryItemId;
                    if (item.quantity !== undefined)
                        updateData.quantity = parseFloat(item.quantity.toFixed(3));
                    if (item.unit)
                        updateData.unit = item.unit;
                    if (item.wastagePercent !== undefined)
                        updateData.wastagePercent = parseFloat(item.wastagePercent.toFixed(2));
                    transaction.push(this.prisma.recipeItem.update({
                        where: { id: item.id },
                        data: updateData
                    }));
                }
                else {
                    if (!item.inventoryItemId || !item.quantity || !item.unit) {
                        throw new common_1.BadRequestException(`Missing required fields for new recipe item.`);
                    }
                    const inventoryItem = await this.prisma.inventoryItem.findUnique({
                        where: { id: item.inventoryItemId, deletedAt: null },
                    });
                    if (!inventoryItem) {
                        throw new common_1.NotFoundException(`Inventory item with ID "${item.inventoryItemId}" not found for new recipe item.`);
                    }
                    if (inventoryItem.unit !== item.unit) {
                        throw new common_1.BadRequestException(`Unit mismatch: Inventory item "${inventoryItem.name}" is in ${inventoryItem.unit}, but new recipe item uses ${item.unit}.`);
                    }
                    transaction.push(this.prisma.recipeItem.create({
                        data: {
                            recipeId: id,
                            inventoryItemId: item.inventoryItemId,
                            quantity: parseFloat(item.quantity.toFixed(3)),
                            unit: item.unit,
                            wastagePercent: item.wastagePercent !== undefined ? parseFloat(item.wastagePercent.toFixed(2)) : 0,
                        }
                    }));
                }
            }
        }
        const updatedRecipeData = {
            productId: data.productId,
            name: data.name,
            yield: data.yield !== undefined ? parseFloat(data.yield.toFixed(3)) : undefined,
            preparationSteps: data.preparationSteps,
            preparationTime: data.preparationTime,
            active: data.active,
        };
        Object.keys(updatedRecipeData).forEach(key => {
            if (updatedRecipeData[key] === undefined) {
                delete updatedRecipeData[key];
            }
        });
        transaction.push(this.prisma.recipe.update({
            where: { id, deletedAt: null },
            data: updatedRecipeData,
        }));
        try {
            await this.prisma.$transaction(transaction);
            return this.findOneRecipe(id);
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Recipe with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeRecipe(id) {
        try {
            return await this.prisma.recipe.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Recipe with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.RecipeService = RecipeService;
exports.RecipeService = RecipeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RecipeService);
//# sourceMappingURL=recipe.service.js.map