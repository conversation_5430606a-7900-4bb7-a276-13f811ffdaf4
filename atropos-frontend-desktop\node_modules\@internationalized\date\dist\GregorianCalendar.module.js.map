{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;;AAMhG,MAAM,8BAAQ,SAAS,wBAAwB;AACxC,SAAS,0CAAqB,GAAW,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW;IACxF,OAAO,0CAAgB,KAAK;IAE5B,IAAI,KAAK,OAAO;IAChB,IAAI,cAAc;IAClB,IAAI,SAAS,GACX,cAAc;SACT,IAAI,0CAAW,OACpB,cAAc;IAGhB,OACE,8BACA,IACA,MAAM,KACN,KAAK,KAAK,CAAC,KAAK,KAChB,KAAK,KAAK,CAAC,KAAK,OAChB,KAAK,KAAK,CAAC,KAAK,OAChB,KAAK,KAAK,CAAC,AAAC,CAAA,MAAM,QAAQ,GAAE,IAAK,KAAK,cAAc;AAExD;AAEO,SAAS,0CAAW,IAAY;IACrC,OAAO,OAAO,MAAM,KAAM,CAAA,OAAO,QAAQ,KAAK,OAAO,QAAQ,CAAA;AAC/D;AAEO,SAAS,0CAAgB,GAAW,EAAE,IAAY;IACvD,OAAO,QAAQ,OAAO,IAAI,OAAO;AACnC;AAEO,SAAS,0CAAiB,IAAY;IAC3C,IAAI,MAAM;IACV,IAAI,QAAQ,GAAG;QACb,MAAM;QACN,OAAO,IAAI;IACb;IAEA,OAAO;QAAC;QAAK;KAAK;AACpB;AAEA,MAAM,oCAAc;IAClB,UAAU;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IAC1D,UAAU;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;AAC5D;AAMO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,MAAM;QACV,IAAI,SAAS,MAAM;QACnB,IAAI,aAAa,KAAK,KAAK,CAAC,SAAS;QACrC,IAAI,MAAM,CAAA,GAAA,yCAAE,EAAE,QAAQ;QACtB,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM;QAC5B,IAAI,QAAQ,CAAA,GAAA,yCAAE,EAAE,KAAK;QACrB,IAAI,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC9B,IAAI,QAAQ,CAAA,GAAA,yCAAE,EAAE,OAAO;QACvB,IAAI,SAAS,KAAK,KAAK,CAAC,QAAQ;QAEhC,IAAI,eAAe,aAAa,MAAM,OAAO,MAAM,OAAO,IAAI,SAAU,CAAA,SAAS,KAAK,WAAW,IAAI,IAAI,CAAA;QACzG,IAAI,CAAC,KAAK,KAAK,GAAG,0CAAiB;QACnC,IAAI,UAAU,MAAM,0CAAqB,KAAK,MAAM,GAAG;QACvD,IAAI,UAAU;QACd,IAAI,MAAM,0CAAqB,KAAK,MAAM,GAAG,IAC3C,UAAU;aACL,IAAI,0CAAW,OACpB,UAAU;QAEZ,IAAI,QAAQ,KAAK,KAAK,CAAC,AAAC,CAAA,AAAC,CAAA,UAAU,OAAM,IAAK,KAAK,GAAE,IAAK;QAC1D,IAAI,MAAM,MAAM,0CAAqB,KAAK,MAAM,OAAO,KAAK;QAE5D,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,KAAK,MAAM,OAAO;IAC5C;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,0CAAqB,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IACvE;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,iCAAW,CAAC,0CAAW,KAAK,IAAI,IAAI,aAAa,WAAW,CAAC,KAAK,KAAK,GAAG,EAAE;IACrF;IAEA,6DAA6D;IAC7D,gBAAgB,IAAqB,EAAU;QAC7C,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,0CAAW,KAAK,IAAI,IAAI,MAAM;IACvC;IAEA,6DAA6D;IAC7D,cAAc,IAAqB,EAAU;QAC3C,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;YAAM;SAAK;IACrB;IAEA,aAAa,IAAqB,EAAW;QAC3C,OAAO,KAAK,GAAG,KAAK;IACtB;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,KAAK,IAAI,IAAI,GAAG;YAClB,KAAK,GAAG,GAAG,KAAK,GAAG,KAAK,OAAO,OAAO;YACtC,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI;QAC3B;IACF;;aA/DA,aAAiC;;AAgEnC", "sources": ["packages/@internationalized/date/src/calendars/GregorianCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst EPOCH = 1721426; // 001/01/03 Julian C.E.\nexport function gregorianToJulianDay(era: string, year: number, month: number, day: number): number {\n  year = getExtendedYear(era, year);\n\n  let y1 = year - 1;\n  let monthOffset = -2;\n  if (month <= 2) {\n    monthOffset = 0;\n  } else if (isLeapYear(year)) {\n    monthOffset = -1;\n  }\n\n  return (\n    EPOCH -\n    1 +\n    365 * y1 +\n    Math.floor(y1 / 4) -\n    Math.floor(y1 / 100) +\n    Math.floor(y1 / 400) +\n    Math.floor((367 * month - 362) / 12 + monthOffset + day)\n  );\n}\n\nexport function isLeapYear(year: number): boolean {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function getExtendedYear(era: string, year: number): number {\n  return era === 'BC' ? 1 - year : year;\n}\n\nexport function fromExtendedYear(year: number): [string, number] {\n  let era = 'AD';\n  if (year <= 0) {\n    era = 'BC';\n    year = 1 - year;\n  }\n\n  return [era, year];\n}\n\nconst daysInMonth = {\n  standard: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n  leapyear: [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\n};\n\n/**\n * The Gregorian calendar is the most commonly used calendar system in the world. It supports two eras: BC, and AD.\n * Years always contain 12 months, and 365 or 366 days depending on whether it is a leap year.\n */\nexport class GregorianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'gregory';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let jd0 = jd;\n    let depoch = jd0 - EPOCH;\n    let quadricent = Math.floor(depoch / 146097);\n    let dqc = mod(depoch, 146097);\n    let cent = Math.floor(dqc / 36524);\n    let dcent = mod(dqc, 36524);\n    let quad = Math.floor(dcent / 1461);\n    let dquad = mod(dcent, 1461);\n    let yindex = Math.floor(dquad / 365);\n\n    let extendedYear = quadricent * 400 + cent * 100 + quad * 4 + yindex + (cent !== 4 && yindex !== 4 ? 1 : 0);\n    let [era, year] = fromExtendedYear(extendedYear);\n    let yearDay = jd0 - gregorianToJulianDay(era, year, 1, 1);\n    let leapAdj = 2;\n    if (jd0 < gregorianToJulianDay(era, year, 3, 1)) {\n      leapAdj = 0;\n    } else if (isLeapYear(year)) {\n      leapAdj = 1;\n    }\n    let month = Math.floor(((yearDay + leapAdj) * 12 + 373) / 367);\n    let day = jd0 - gregorianToJulianDay(era, year, month, 1) + 1;\n\n    return new CalendarDate(era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return gregorianToJulianDay(date.era, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return daysInMonth[isLeapYear(date.year) ? 'leapyear' : 'standard'][date.month - 1];\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 366 : 365;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getYearsInEra(date: AnyCalendarDate): number {\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['BC', 'AD'];\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BC';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BC' ? 'AD' : 'BC';\n      date.year = 1 - date.year;\n    }\n  }\n}\n"], "names": [], "version": 3, "file": "GregorianCalendar.module.js.map"}