"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourierLocationModule = void 0;
const common_1 = require("@nestjs/common");
const courier_location_service_1 = require("./courier-location.service");
const courier_location_controller_1 = require("./courier-location.controller");
const prisma_module_1 = require("../prisma/prisma.module");
let CourierLocationModule = class CourierLocationModule {
};
exports.CourierLocationModule = CourierLocationModule;
exports.CourierLocationModule = CourierLocationModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [courier_location_controller_1.CourierLocationController],
        providers: [courier_location_service_1.CourierLocationService],
        exports: [courier_location_service_1.CourierLocationService],
    })
], CourierLocationModule);
//# sourceMappingURL=courier-location.module.js.map