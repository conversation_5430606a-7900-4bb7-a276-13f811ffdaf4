import { PrismaService } from '../prisma/prisma.service';
import { CreatePrinterGroupDto } from './dto/create-printer-group.dto';
import { UpdatePrinterGroupDto } from './dto/update-printer-group.dto';
export declare class PrinterGroupService {
    private prisma;
    constructor(prisma: PrismaService);
    createPrinterGroup(data: CreatePrinterGroupDto): Promise<{
        categories: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    }>;
    findAllPrinterGroups(): Promise<({
        categories: {
            name: string;
            id: string;
        }[];
        printers: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            active: boolean;
            branchId: string;
            printerGroupId: string | null;
            type: import("generated/prisma").$Enums.PrinterType;
            connectionType: string;
            ipAddress: string | null;
            port: number | null;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    })[]>;
    findOnePrinterGroup(id: string): Promise<{
        categories: {
            name: string;
            id: string;
        }[];
        printers: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            active: boolean;
            branchId: string;
            printerGroupId: string | null;
            type: import("generated/prisma").$Enums.PrinterType;
            connectionType: string;
            ipAddress: string | null;
            port: number | null;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    }>;
    updatePrinterGroup(id: string, data: UpdatePrinterGroupDto): Promise<{
        categories: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    }>;
    removePrinterGroup(id: string): Promise<{
        name: string;
        id: string;
        categoryIds: string[];
    }>;
}
