import { PrismaService } from '../prisma/prisma.service';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationChannel } from '../../generated/prisma';
export declare class NotificationTemplateService {
    private prisma;
    constructor(prisma: PrismaService);
    createNotificationTemplate(data: CreateNotificationTemplateDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    findAllNotificationTemplates(companyId?: string, channel?: NotificationChannel, active?: boolean): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    })[]>;
    findOneNotificationTemplate(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    updateNotificationTemplate(id: string, data: UpdateNotificationTemplateDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    removeNotificationTemplate(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
}
