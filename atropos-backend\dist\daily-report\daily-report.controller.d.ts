import { DailyReportService } from './daily-report.service';
import { CreateDailyReportDto } from './dto/create-daily-report.dto';
import { UpdateDailyReportDto } from './dto/update-daily-report.dto';
export declare class DailyReportController {
    private readonly dailyReportService;
    constructor(dailyReportService: DailyReportService);
    create(createDailyReportDto: CreateDailyReportDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
    findAll(branchId?: string, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
    update(id: string, updateDailyReportDto: UpdateDailyReportDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
}
