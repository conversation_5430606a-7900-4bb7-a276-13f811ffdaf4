"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineProductMappingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let OnlineProductMappingService = class OnlineProductMappingService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createOnlineProductMapping(data) {
        const platformExists = await this.prisma.onlinePlatform.findUnique({
            where: { id: data.platformId },
        });
        if (!platformExists) {
            throw new common_1.NotFoundException(`Online platform with ID "${data.platformId}" not found.`);
        }
        const productExists = await this.prisma.product.findUnique({
            where: { id: data.productId, deletedAt: null },
        });
        if (!productExists) {
            throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
        }
        const existingMappingByProductId = await this.prisma.onlineProductMapping.findUnique({
            where: {
                platformId_productId: {
                    platformId: data.platformId,
                    productId: data.productId,
                },
            },
        });
        if (existingMappingByProductId) {
            throw new common_1.ConflictException(`Product mapping already exists for product "${data.productId}" on platform "${data.platformId}".`);
        }
        const existingMappingByPlatformProductId = await this.prisma.onlineProductMapping.findUnique({
            where: {
                platformId_platformProductId: {
                    platformId: data.platformId,
                    platformProductId: data.platformProductId,
                },
            },
        });
        if (existingMappingByPlatformProductId) {
            throw new common_1.ConflictException(`Platform product ID "${data.platformProductId}" is already mapped on platform "${data.platformId}".`);
        }
        return this.prisma.onlineProductMapping.create({
            data: {
                ...data,
                priceOverride: data.priceOverride !== undefined ? parseFloat(data.priceOverride.toFixed(2)) : undefined,
            },
        });
    }
    async findAllOnlineProductMappings(platformId, productId) {
        return this.prisma.onlineProductMapping.findMany({
            where: {
                platformId: platformId || undefined,
                productId: productId || undefined,
            },
            include: {
                platform: { select: { id: true, name: true, code: true } },
                product: { select: { id: true, name: true, code: true, basePrice: true } },
            },
            orderBy: { id: 'desc' },
        });
    }
    async findOneOnlineProductMapping(id) {
        const mapping = await this.prisma.onlineProductMapping.findUnique({
            where: { id },
            include: {
                platform: { select: { id: true, name: true, code: true } },
                product: { select: { id: true, name: true, code: true, basePrice: true } },
            },
        });
        if (!mapping) {
            throw new common_1.NotFoundException(`Online product mapping with ID "${id}" not found.`);
        }
        return mapping;
    }
    async updateOnlineProductMapping(id, data) {
        if (data.platformId) {
            const platformExists = await this.prisma.onlinePlatform.findUnique({ where: { id: data.platformId } });
            if (!platformExists) {
                throw new common_1.NotFoundException(`Online platform with ID "${data.platformId}" not found.`);
            }
        }
        if (data.productId) {
            const productExists = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
            if (!productExists) {
                throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
            }
        }
        if (data.platformId || data.productId || data.platformProductId) {
            const currentMapping = await this.findOneOnlineProductMapping(id);
            const targetPlatformId = data.platformId || currentMapping.platformId;
            if (data.productId && data.productId !== currentMapping.productId) {
                const existingMappingByProductId = await this.prisma.onlineProductMapping.findUnique({
                    where: { platformId_productId: { platformId: targetPlatformId, productId: data.productId } },
                });
                if (existingMappingByProductId && existingMappingByProductId.id !== id) {
                    throw new common_1.ConflictException(`Product mapping already exists for product "${data.productId}" on platform "${targetPlatformId}".`);
                }
            }
            if (data.platformProductId && data.platformProductId !== currentMapping.platformProductId) {
                const existingMappingByPlatformProductId = await this.prisma.onlineProductMapping.findUnique({
                    where: { platformId_platformProductId: { platformId: targetPlatformId, platformProductId: data.platformProductId } },
                });
                if (existingMappingByPlatformProductId && existingMappingByPlatformProductId.id !== id) {
                    throw new common_1.ConflictException(`Platform product ID "${data.platformProductId}" is already mapped on platform "${targetPlatformId}".`);
                }
            }
        }
        try {
            return await this.prisma.onlineProductMapping.update({
                where: { id },
                data: {
                    ...data,
                    priceOverride: data.priceOverride !== undefined ? parseFloat(data.priceOverride.toFixed(2)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Online product mapping with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeOnlineProductMapping(id) {
        try {
            return await this.prisma.onlineProductMapping.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Online product mapping with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.OnlineProductMappingService = OnlineProductMappingService;
exports.OnlineProductMappingService = OnlineProductMappingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OnlineProductMappingService);
//# sourceMappingURL=online-product-mapping.service.js.map