"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BranchService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let BranchService = class BranchService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createBranch(data) {
        const existingBranch = await this.prisma.branch.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingBranch) {
            throw new common_1.ConflictException(`Branch with code "${data.code}" already exists for this company.`);
        }
        return this.prisma.branch.create({ data });
    }
    async findAllBranches(companyId) {
        return this.prisma.branch.findMany({
            where: { companyId: companyId || undefined, deletedAt: null },
            include: { company: true },
        });
    }
    async findOneBranch(id) {
        const branch = await this.prisma.branch.findUnique({
            where: { id, deletedAt: null },
            include: { company: true },
        });
        if (!branch) {
            throw new common_1.NotFoundException(`Branch with ID "${id}" not found.`);
        }
        return branch;
    }
    async updateBranch(id, data) {
        try {
            if (data.code || data.companyId) {
                const existingBranch = await this.prisma.branch.findUnique({
                    where: {
                        companyId_code: {
                            companyId: data.companyId || (await this.findOneBranch(id)).companyId,
                            code: data.code || (await this.findOneBranch(id)).code,
                        },
                    },
                });
                if (existingBranch && existingBranch.id !== id) {
                    throw new common_1.ConflictException(`Branch with code "${data.code}" already exists for this company.`);
                }
            }
            return await this.prisma.branch.update({
                where: { id, deletedAt: null },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Branch with ID "${id}" not found.`);
            }
            if (error.code === 'P2002') {
                throw new common_1.ConflictException(`Branch with this code already exists for the company.`);
            }
            throw error;
        }
    }
    async removeBranch(id) {
        try {
            return await this.prisma.branch.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date() },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Branch with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.BranchService = BranchService;
exports.BranchService = BranchService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], BranchService);
//# sourceMappingURL=branch.service.js.map