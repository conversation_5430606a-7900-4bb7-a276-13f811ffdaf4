declare enum ProductUnit {
    PIECE = "PIECE",
    KG = "KG",
    GRAM = "GRAM",
    LITER = "LITER",
    ML = "ML"
}
export declare class UpdateRecipeItemDto {
    id?: string;
    inventoryItemId?: string;
    quantity?: number;
    unit?: ProductUnit;
    wastagePercent?: number;
}
export declare class UpdateRecipeDto {
    productId?: string;
    name?: string;
    yield?: number;
    preparationSteps?: string;
    preparationTime?: number;
    active?: boolean;
    items?: UpdateRecipeItemDto[];
}
export {};
