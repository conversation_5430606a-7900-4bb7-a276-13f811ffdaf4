import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
export declare class CompanyController {
    private readonly companyService;
    constructor(companyService: CompanyService);
    create(createCompanyDto: CreateCompanyDto, req: any): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    findAll(): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }[]>;
    findOne(id: string): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
}
