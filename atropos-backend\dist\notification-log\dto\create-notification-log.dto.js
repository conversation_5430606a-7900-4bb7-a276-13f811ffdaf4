"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNotificationLogDto = void 0;
const class_validator_1 = require("class-validator");
const prisma_1 = require("../../../generated/prisma");
class CreateNotificationLogDto {
    templateId;
    recipient;
    channel;
    status;
    message;
    response;
    failedReason;
}
exports.CreateNotificationLogDto = CreateNotificationLogDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateNotificationLogDto.prototype, "templateId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateNotificationLogDto.prototype, "recipient", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(prisma_1.NotificationChannel),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateNotificationLogDto.prototype, "channel", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(prisma_1.NotificationStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateNotificationLogDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateNotificationLogDto.prototype, "message", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateNotificationLogDto.prototype, "response", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateNotificationLogDto.prototype, "failedReason", void 0);
//# sourceMappingURL=create-notification-log.dto.js.map