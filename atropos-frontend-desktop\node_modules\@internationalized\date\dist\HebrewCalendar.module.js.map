{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;;AAMhG,MAAM,qCAAe;AAErB,sEAAsE;AACtE,uEAAuE;AACvE,MAAM,mCAAa;AACnB,MAAM,kCAAa,KAAK;AAExB,wDAAwD;AACxD,oEAAoE;AACpE,iBAAiB;AACjB,MAAM,mCAAa;AACnB,MAAM,oCAAc,KAAK,mCAAa;AACtC,MAAM,oCAAc,mCAAa,kCAAY;AAE7C,SAAS,iCAAW,IAAY;IAC9B,OAAO,CAAA,GAAA,yCAAE,EAAE,OAAO,IAAI,GAAG,MAAM;AACjC;AAEA,mDAAmD;AACnD,0DAA0D;AAC1D,SAAS,mCAAa,IAAY;IAChC,IAAI,SAAS,KAAK,KAAK,CAAC,AAAC,CAAA,MAAM,OAAO,GAAE,IAAK;IAC7C,IAAI,QAAQ,QAAQ,QAAQ;IAC5B,IAAI,MAAM,SAAS,KAAK,KAAK,KAAK,CAAC,QAAQ;IAE3C,IAAI,CAAA,GAAA,yCAAE,EAAE,IAAK,CAAA,MAAM,CAAA,GAAI,KAAK,GAC1B,OAAO;IAGT,OAAO;AACT;AAEA,uEAAuE;AACvE,SAAS,mCAAa,IAAY;IAChC,IAAI,OAAO,mCAAa,OAAO;IAC/B,IAAI,UAAU,mCAAa;IAC3B,IAAI,OAAO,mCAAa,OAAO;IAE/B,IAAI,OAAO,YAAY,KACrB,OAAO;IAGT,IAAI,UAAU,SAAS,KACrB,OAAO;IAGT,OAAO;AACT;AAEA,SAAS,kCAAY,IAAY;IAC/B,OAAO,mCAAa,QAAQ,mCAAa;AAC3C;AAEA,SAAS,oCAAc,IAAY;IACjC,OAAO,kCAAY,OAAO,KAAK,kCAAY;AAC7C;AAEA,SAAS,kCAAY,IAAY;IAC/B,IAAI,aAAa,oCAAc;IAE/B,IAAI,aAAa,KACf,cAAc,IAAI,iCAAiC;IAGrD,OAAQ;QACN,KAAK;YACH,OAAO,GAAG,YAAY;QACxB,KAAK;YACH,OAAO,GAAG,SAAS;QACrB,KAAK;YACH,OAAO,GAAG,WAAW;IACzB;AACF;AAEA,SAAS,qCAAe,IAAY,EAAE,KAAa;IACjD,8DAA8D;IAC9D,IAAI,SAAS,KAAK,CAAC,iCAAW,OAC5B;IAGF,sDAAsD;IACtD,IAAI,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU,IACzE,OAAO;IAGT,IAAI,WAAW,kCAAY;IAE3B,iDAAiD;IACjD,IAAI,UAAU,GACZ,OAAO,aAAa,IAAI,KAAK;IAG/B,mDAAmD;IACnD,IAAI,UAAU,GACZ,OAAO,aAAa,IAAI,KAAK;IAG/B,mCAAmC;IACnC,IAAI,UAAU,GACZ,OAAO,iCAAW,QAAQ,KAAK;IAGjC,OAAO;AACT;AAOO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,IAAI,KAAK;QACb,IAAI,IAAI,AAAC,IAAI,kCAAa,mCAAuB,kBAAkB;QACnE,IAAI,OAAO,KAAK,KAAK,CAAC,AAAC,CAAA,KAAK,IAAI,GAAE,IAAK,OAAO,GAAG,iBAAiB;QAClE,IAAI,KAAK,kCAAY,OAA4B,kBAAkB;QACnE,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI;QAE/B,4EAA4E;QAC5E,MAAO,YAAY,EAAG;YACpB;YACA,KAAK,kCAAY;YACjB,YAAY,KAAK,KAAK,CAAC,IAAI;QAC7B;QAEA,sEAAsE;QACtE,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,MAAO,aAAa,UAAW;YAC7B,cAAc,qCAAe,MAAM;YACnC;QACF;QAEA;QACA,cAAc,qCAAe,MAAM;QAEnC,IAAI,MAAM,YAAY;QACtB,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,MAAM,OAAO;IAC7C;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,KAAK,kCAAY,KAAK,IAAI;QAC9B,IAAK,IAAI,QAAQ,GAAG,QAAQ,KAAK,KAAK,EAAE,QACtC,MAAM,qCAAe,KAAK,IAAI,EAAE;QAGlC,OAAO,KAAK,KAAK,GAAG,GAAG;IACzB;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,qCAAe,KAAK,IAAI,EAAE,KAAK,KAAK;IAC7C;IAEA,gBAAgB,IAAqB,EAAU;QAC7C,OAAO,iCAAW,KAAK,IAAI,IAAI,KAAK;IACtC;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,oCAAc,KAAK,IAAI;IAChC;IAEA,gBAAwB;QACtB,iBAAiB;QACjB,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;IAEA,iBAAiB,IAA8B,EAAE,YAA6B,EAAQ;QACpF,mFAAmF;QACnF,IAAI,aAAa,IAAI,KAAK,KAAK,IAAI,EAAE;YACnC,IAAI,iCAAW,aAAa,IAAI,KAAK,CAAC,iCAAW,KAAK,IAAI,KAAK,aAAa,KAAK,GAAG,GAClF,KAAK,KAAK;iBACL,IAAI,CAAC,iCAAW,aAAa,IAAI,KAAK,iCAAW,KAAK,IAAI,KAAK,aAAa,KAAK,GAAG,GACzF,KAAK,KAAK;QAEd;IACF;;aAtEA,aAAiC;;AAuEnC", "sources": ["packages/@internationalized/date/src/calendars/HebrewCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst HEBREW_EPOCH = 347997;\n\n// Hebrew date calculations are performed in terms of days, hours, and\n// \"parts\" (or halakim), which are 1/1080 of an hour, or 3 1/3 seconds.\nconst HOUR_PARTS = 1080;\nconst DAY_PARTS  = 24 * HOUR_PARTS;\n\n// An approximate value for the length of a lunar month.\n// It is used to calculate the approximate year and month of a given\n// absolute date.\nconst MONTH_DAYS = 29;\nconst MONTH_FRACT = 12 * HOUR_PARTS + 793;\nconst MONTH_PARTS = MONTH_DAYS * DAY_PARTS + MONTH_FRACT;\n\nfunction isLeapYear(year: number) {\n  return mod(year * 7 + 1, 19) < 7;\n}\n\n// Test for delay of start of new year and to avoid\n// Sunday, Wednesday, and Friday as start of the new year.\nfunction hebrewDelay1(year: number) {\n  let months = Math.floor((235 * year - 234) / 19);\n  let parts = 12084 + 13753 * months;\n  let day = months * 29 + Math.floor(parts / 25920);\n\n  if (mod(3 * (day + 1), 7) < 3) {\n    day += 1;\n  }\n\n  return day;\n}\n\n// Check for delay in start of new year due to length of adjacent years\nfunction hebrewDelay2(year: number) {\n  let last = hebrewDelay1(year - 1);\n  let present = hebrewDelay1(year);\n  let next = hebrewDelay1(year + 1);\n\n  if (next - present === 356) {\n    return 2;\n  }\n\n  if (present - last === 382) {\n    return 1;\n  }\n\n  return 0;\n}\n\nfunction startOfYear(year: number) {\n  return hebrewDelay1(year) + hebrewDelay2(year);\n}\n\nfunction getDaysInYear(year: number) {\n  return startOfYear(year + 1) - startOfYear(year);\n}\n\nfunction getYearType(year: number) {\n  let yearLength = getDaysInYear(year);\n\n  if (yearLength > 380) {\n    yearLength -= 30; // Subtract length of leap month.\n  }\n\n  switch (yearLength) {\n    case 353:\n      return 0; // deficient\n    case 354:\n      return 1; // normal\n    case 355:\n      return 2; // complete\n  }\n}\n\nfunction getDaysInMonth(year: number, month: number): number {\n  // Normalize month numbers from 1 - 13, even on non-leap years\n  if (month >= 6 && !isLeapYear(year)) {\n    month++;\n  }\n\n  // First of all, dispose of fixed-length 29 day months\n  if (month === 4 || month === 7 || month === 9 || month === 11 || month === 13) {\n    return 29;\n  }\n\n  let yearType = getYearType(year);\n\n  // If it's Heshvan, days depend on length of year\n  if (month === 2) {\n    return yearType === 2 ? 30 : 29;\n  }\n\n  // Similarly, Kislev varies with the length of year\n  if (month === 3) {\n    return yearType === 0 ? 29 : 30;\n  }\n\n  // Adar I only exists in leap years\n  if (month === 6) {\n    return isLeapYear(year) ? 30 : 0;\n  }\n\n  return 30;\n}\n\n/**\n * The Hebrew calendar is used in Israel and around the world by the Jewish faith.\n * Years include either 12 or 13 months depending on whether it is a leap year.\n * In leap years, an extra month is inserted at month 6.\n */\nexport class HebrewCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'hebrew';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let d = jd - HEBREW_EPOCH;\n    let m = (d * DAY_PARTS) / MONTH_PARTS;           // Months (approx)\n    let year = Math.floor((19 * m + 234) / 235) + 1; // Years (approx)\n    let ys = startOfYear(year);                      // 1st day of year\n    let dayOfYear = Math.floor(d - ys);\n\n    // Because of the postponement rules, it's possible to guess wrong.  Fix it.\n    while (dayOfYear < 1) {\n      year--;\n      ys = startOfYear(year);\n      dayOfYear = Math.floor(d - ys);\n    }\n\n    // Now figure out which month we're in, and the date within that month\n    let month = 1;\n    let monthStart = 0;\n    while (monthStart < dayOfYear) {\n      monthStart += getDaysInMonth(year, month);\n      month++;\n    }\n\n    month--;\n    monthStart -= getDaysInMonth(year, month);\n\n    let day = dayOfYear - monthStart;\n    return new CalendarDate(this, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = startOfYear(date.year);\n    for (let month = 1; month < date.month; month++) {\n      jd += getDaysInMonth(date.year, month);\n    }\n\n    return jd + date.day + HEBREW_EPOCH;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 13 : 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return getDaysInYear(date.year);\n  }\n\n  getYearsInEra(): number {\n    // 6239 gregorian\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['AM'];\n  }\n\n  balanceYearMonth(date: Mutable<AnyCalendarDate>, previousDate: AnyCalendarDate): void {\n    // Keep date in the same month when switching between leap years and non leap years\n    if (previousDate.year !== date.year) {\n      if (isLeapYear(previousDate.year) && !isLeapYear(date.year) && previousDate.month > 6) {\n        date.month--;\n      } else if (!isLeapYear(previousDate.year) && isLeapYear(date.year) && previousDate.month > 6) {\n        date.month++;\n      }\n    }\n  }\n}\n"], "names": [], "version": 3, "file": "HebrewCalendar.module.js.map"}