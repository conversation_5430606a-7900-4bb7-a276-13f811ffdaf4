{"version": 3, "file": "online-platform.service.js", "sourceRoot": "", "sources": ["../../src/online-platform/online-platform.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,2CAAkF;AAClF,6DAAyD;AAKlD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IACZ;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,oBAAoB,CAAC,IAA6B;QAEtD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;SAC/C,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,IAAI,CAAC,SAAS,cAAc,CAAC,CAAC;QAChF,CAAC;QAGD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;YACnE,KAAK,EAAE;gBACL,cAAc,EAAE;oBACd,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB;aACF;SACF,CAAC,CAAC;QACH,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,IAAI,CAAC,IAAI,oCAAoC,CAAC,CAAC;QAC3G,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACvC,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,cAAc,EAAE,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;aAC3G;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAkB;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACzC,KAAK,EAAE,EAAE,SAAS,EAAE,SAAS,IAAI,SAAS,EAAE;YAC5C,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;YAC1D,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,EAAU;QACpC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;SAC3D,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,EAAU,EAAE,IAA6B;QAElE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;aACjD,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,IAAI,CAAC,SAAS,cAAc,CAAC,CAAC;YAClF,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAC7D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;gBACjE,KAAK,EAAE;oBACH,cAAc,EAAE;wBACZ,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,eAAe,CAAC,SAAS;wBACtD,IAAI,EAAE,IAAI,CAAC,IAAI;qBAClB;iBACJ;aACJ,CAAC,CAAC;YACH,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACjD,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,IAAI,CAAC,IAAI,oCAAoC,CAAC,CAAC;YAC7G,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,IAAI,EAAE;oBACF,GAAG,IAAI;oBACP,cAAc,EAAE,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC7G;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC;YAC5E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,EAAU;QAEnC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;YAC1D,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;SAC5B,CAAC,CAAC;QACH,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,sCAAsC,iBAAiB,4BAA4B,CAAC,CAAC;QACnJ,CAAC;QAED,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;YACtE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;SAC5B,CAAC,CAAC;QACH,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,sCAAsC,oBAAoB,+BAA+B,CAAC,CAAC;QACzJ,CAAC;QAGD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC;YAC5E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA3HY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,qBAAqB,CA2HjC"}