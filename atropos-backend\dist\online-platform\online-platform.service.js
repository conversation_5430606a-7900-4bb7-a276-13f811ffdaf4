"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlinePlatformService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let OnlinePlatformService = class OnlinePlatformService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createOnlinePlatform(data) {
        const companyExists = await this.prisma.company.findUnique({
            where: { id: data.companyId, deletedAt: null },
        });
        if (!companyExists) {
            throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
        const existingPlatform = await this.prisma.onlinePlatform.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingPlatform) {
            throw new common_1.ConflictException(`Online platform with code "${data.code}" already exists for this company.`);
        }
        return this.prisma.onlinePlatform.create({
            data: {
                ...data,
                commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
            },
        });
    }
    async findAllOnlinePlatforms(companyId) {
        return this.prisma.onlinePlatform.findMany({
            where: { companyId: companyId || undefined },
            include: { company: { select: { id: true, name: true } } },
            orderBy: { name: 'asc' },
        });
    }
    async findOneOnlinePlatform(id) {
        const platform = await this.prisma.onlinePlatform.findUnique({
            where: { id },
            include: { company: { select: { id: true, name: true } } },
        });
        if (!platform) {
            throw new common_1.NotFoundException(`Online platform with ID "${id}" not found.`);
        }
        return platform;
    }
    async updateOnlinePlatform(id, data) {
        if (data.companyId) {
            const companyExists = await this.prisma.company.findUnique({
                where: { id: data.companyId, deletedAt: null },
            });
            if (!companyExists) {
                throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
            }
        }
        if (data.code) {
            const currentPlatform = await this.findOneOnlinePlatform(id);
            const existingPlatform = await this.prisma.onlinePlatform.findUnique({
                where: {
                    companyId_code: {
                        companyId: data.companyId || currentPlatform.companyId,
                        code: data.code,
                    },
                },
            });
            if (existingPlatform && existingPlatform.id !== id) {
                throw new common_1.ConflictException(`Online platform with code "${data.code}" already exists for this company.`);
            }
        }
        try {
            return await this.prisma.onlinePlatform.update({
                where: { id },
                data: {
                    ...data,
                    commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Online platform with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeOnlinePlatform(id) {
        const onlineOrdersCount = await this.prisma.onlineOrder.count({
            where: { platformId: id }
        });
        if (onlineOrdersCount > 0) {
            throw new common_1.ConflictException(`Online platform with ID "${id}" cannot be deleted because it has ${onlineOrdersCount} associated online orders.`);
        }
        const productMappingsCount = await this.prisma.onlineProductMapping.count({
            where: { platformId: id }
        });
        if (productMappingsCount > 0) {
            throw new common_1.ConflictException(`Online platform with ID "${id}" cannot be deleted because it has ${productMappingsCount} associated product mappings.`);
        }
        try {
            return await this.prisma.onlinePlatform.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Online platform with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.OnlinePlatformService = OnlinePlatformService;
exports.OnlinePlatformService = OnlinePlatformService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OnlinePlatformService);
//# sourceMappingURL=online-platform.service.js.map