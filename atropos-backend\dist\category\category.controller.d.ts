import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
export declare class CategoryController {
    private readonly categoryService;
    constructor(categoryService: CategoryService);
    create(createCategoryDto: CreateCategoryDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
    findAll(companyId?: string, parentId?: string): Promise<({
        parent: {
            name: string;
            id: string;
        } | null;
        children: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    })[]>;
    findOne(id: string): Promise<{
        parent: {
            name: string;
            id: string;
        } | null;
        children: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
    update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
}
