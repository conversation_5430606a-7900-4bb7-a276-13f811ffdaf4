"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourierLocationController = void 0;
const common_1 = require("@nestjs/common");
const courier_location_service_1 = require("./courier-location.service");
const create_courier_location_dto_1 = require("./dto/create-courier-location.dto");
const update_courier_location_dto_1 = require("./dto/update-courier-location.dto");
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
let CourierLocationController = class CourierLocationController {
    courierLocationService;
    constructor(courierLocationService) {
        this.courierLocationService = courierLocationService;
    }
    create(createCourierLocationDto) {
        return this.courierLocationService.createCourierLocation(createCourierLocationDto);
    }
    findAll(courierId, branchId, startDate, endDate) {
        return this.courierLocationService.findAllCourierLocations(courierId, branchId, startDate, endDate);
    }
    findOne(id) {
        return this.courierLocationService.findOneCourierLocation(id);
    }
    update(id, updateCourierLocationDto) {
        return this.courierLocationService.updateCourierLocation(id, updateCourierLocationDto);
    }
    remove(id) {
        return this.courierLocationService.removeCourierLocation(id);
    }
};
exports.CourierLocationController = CourierLocationController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_courier_location_dto_1.CreateCourierLocationDto]),
    __metadata("design:returntype", void 0)
], CourierLocationController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('courierId')),
    __param(1, (0, common_1.Query)('branchId')),
    __param(2, (0, common_1.Query)('startDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __param(3, (0, common_1.Query)('endDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Date,
        Date]),
    __metadata("design:returntype", void 0)
], CourierLocationController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CourierLocationController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_courier_location_dto_1.UpdateCourierLocationDto]),
    __metadata("design:returntype", void 0)
], CourierLocationController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CourierLocationController.prototype, "remove", null);
exports.CourierLocationController = CourierLocationController = __decorate([
    (0, common_1.Controller)('courier-location'),
    __metadata("design:paramtypes", [courier_location_service_1.CourierLocationService])
], CourierLocationController);
//# sourceMappingURL=courier-location.controller.js.map