{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AASD,MAAM,gCAAU;AAChB,MAAM,gCAAU;AAChB,MAAM,qCAAe;AACrB,MAAM,2CAAqB;AAC3B,MAAM,oCAAc;AACpB,MAAM,8CACF;AACJ,MAAM,mDAA6B;IAAC;IAAS;IAAW;CAAU;AAClE,MAAM,+CAAyB;IAAC;IAAS;IAAU;IAAS;OAAW;CAA2B;AAG3F,SAAS,0CAAU,KAAa;IACrC,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,mCAAmC;IAGrD,OAAO,IAAI,CAAA,GAAA,wCAAG,EACZ,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;AAEnD;AAGO,SAAS,yCAAU,KAAa;IACrC,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,mCAAmC;IAGrD,IAAI,OAA8B,IAAI,CAAA,GAAA,yCAAW,EAC/C,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,OACrB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB;IAGF,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC7D,OAAO;AACT;AAGO,SAAS,0CAAc,KAAa;IACzC,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,wCAAwC;IAG1D,IAAI,OAAO,kCAAY,CAAC,CAAC,EAAE,EAAE,OAAO;IACpC,IAAI,MAAM,OAAO,IAAI,OAAO;IAE5B,IAAI,OAAkC,IAAI,CAAA,GAAA,yCAAe,EACvD,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,GACA,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;IAGjD,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC7D,OAAO;AACT;AAQO,SAAS,0CAAmB,KAAa,EAAE,cAA+B;IAC/E,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,wCAAwC;IAG1D,IAAI,OAAO,kCAAY,CAAC,CAAC,EAAE,EAAE,OAAO;IACpC,IAAI,MAAM,OAAO,IAAI,OAAO;IAE5B,IAAI,OAA+B,IAAI,CAAA,GAAA,yCAAY,EACjD,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,GACA,CAAC,CAAC,GAAG,EACL,GACA,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;IAGjD,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;IAE7D,IAAI,gBAAgB,CAAA,GAAA,yCAAiB,EAAE;IAEvC,IAAI;IACJ,IAAI,CAAC,CAAC,EAAE,EAAE;YACgE;QAAxE,KAAK,MAAM,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,KAAK,MAAvB,UAA8C,kCAAY,CAAA,MAAA,CAAC,CAAC,EAAE,cAAJ,iBAAA,MAAQ,KAAK,GAAG,MAA5B;QAC5D,KAAK,CAAA,GAAA,wCAAY,EAAE,QAAyB,KAAK,MAAM;QAEvD,uCAAuC;QACvC,IAAI,YAAY,CAAA,GAAA,yCAAgB,EAAE,eAAe,KAAK,QAAQ;QAC9D,IAAI,CAAC,UAAU,QAAQ,CAAC,KACtB,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,qCAAe,KAAK,MAAM,EAAE,gBAAgB,EAAE,0CAAiB,MAAM,IAAI,EAAE,KAAK,QAAQ,EAAE;IAExH,OACE,gEAAgE;IAChE,KAAK,CAAA,GAAA,yCAAS,EAAE,CAAA,GAAA,yCAAiB,EAAE,gBAAgB,KAAK,QAAQ,EAAE;IAGpE,OAAO,CAAA,GAAA,yCAAW,EAAE,IAAI,KAAK,QAAQ;AACvC;AAMO,SAAS,0CAAc,KAAa,EAAE,QAAgB;IAC3D,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,wCAAwC;IAG1D,IAAI,OAAO,kCAAY,CAAC,CAAC,EAAE,EAAE,OAAO;IACpC,IAAI,MAAM,OAAO,IAAI,OAAO;IAE5B,IAAI,OAA+B,IAAI,CAAA,GAAA,yCAAY,EACjD,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,GACA,UACA,GACA,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;IAGjD,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;QAGa;IAD1E,IAAI,CAAC,CAAC,EAAE,EACN,KAAK,MAAM,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,KAAK,MAAvB,UAA8C,kCAAY,CAAA,MAAA,CAAC,CAAC,EAAE,cAAJ,iBAAA,MAAQ,KAAK,GAAG,MAA5B;IAG9D,OAAO,CAAA,GAAA,yCAAS,EAAE,MAAuB;AAC3C;AAMO,SAAS,0CAAqB,KAAa;IAChD,OAAO,0CAAc,OAAO,CAAA,GAAA,yCAAe;AAC7C;AAEA,SAAS,kCAAY,KAAa,EAAE,GAAW,EAAE,GAAW;IAC1D,IAAI,MAAM,OAAO;IACjB,IAAI,MAAM,OAAO,MAAM,KACrB,MAAM,IAAI,WAAW,CAAC,oBAAoB,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;IAGvE,OAAO;AACT;AAEO,SAAS,0CAAa,IAAU;IACrC,OAAO,GAAG,OAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,GAAG,OAAO,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW,GAAG,MAAM,KAAK,CAAC,KAAK,IAAI;AACnM;AAEO,SAAS,0CAAa,IAAkB;IAC7C,IAAI,gBAAgB,CAAA,GAAA,yCAAS,EAAE,MAAM,IAAI,CAAA,GAAA,yCAAgB;IACzD,IAAI;IACJ,IAAI,cAAc,GAAG,KAAK,MACxB,OAAO,cAAc,IAAI,KAAK,IAC1B,SACA,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,cAAc,IAAI,GAAG,QAAQ,CAAC,GAAG;SAE/D,OAAO,OAAO,cAAc,IAAI,EAAE,QAAQ,CAAC,GAAG;IAEhD,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,cAAc,KAAK,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,cAAc,GAAG,EAAE,QAAQ,CAAC,GAAG,MAAM;AAChH;AAEO,SAAS,0CAAiB,IAAiB;IAChD,aAAa;IACb,OAAO,GAAG,0CAAa,MAAM,CAAC,EAAE,0CAAa,OAAO;AACtD;AAEA,SAAS,qCAAe,MAAc;IACpC,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,IAAI,MAAM;IACzC,SAAS,KAAK,GAAG,CAAC;IAClB,IAAI,cAAc,KAAK,KAAK,CAAC,SAAU;IACvC,IAAI,gBAAgB,AAAC,SAAU,UAAoB;IACnD,OAAO,GAAG,OAAO,OAAO,aAAa,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,eAAe,QAAQ,CAAC,GAAG,MAAM;AACnG;AAEO,SAAS,0CAAsB,IAAmB;IACvD,OAAO,GAAG,0CAAiB,QAAQ,qCAAe,KAAK,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;AACpF;AAOO,SAAS,0CAAc,KAAa;QAsBpB,eAQc,gBAUP,gBACC,gBACD,gBACD,gBACC,gBACE,gBACA;IA7C9B,MAAM,QAAQ,MAAM,KAAK,CAAC;IAE1B,IAAI,CAAC,OACH,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;IAG9D,MAAM,qBAAqB,CACzB,OACA;QAEA,IAAI,CAAC,OACH,OAAO;QAET,IAAI;YACF,MAAM,OAAO,aAAa,KAAK;YAC/B,OAAO,OAAO,OAAO,MAAM,OAAO,CAAC,KAAK;QAC1C,EAAE,OAAM;YACN,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;QAC9D;IACF;IAEA,MAAM,aAAa,CAAC,GAAC,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,QAAQ;IAE3C,MAAM,oBAAoB,6CAAuB,IAAI,CAAC,CAAA;YAAS;gBAAA,gBAAA,MAAM,MAAM,cAAZ,oCAAA,aAAc,CAAC,MAAM;;IAEpF,IAAI,CAAC,mBACH,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;IAG9D,MAAM,8BAA6B,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,IAAI;IAErD,IAAI,4BAA4B;QAC9B,MAAM,gCAAgC,iDAA2B,IAAI,CAAC,CAAA;gBAAS;oBAAA,gBAAA,MAAM,MAAM,cAAZ,oCAAA,aAAc,CAAC,MAAM;;QACpG,IAAI,CAAC,+BACH,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;IAEhE;IAEA,MAAM,WAAsC;QAC1C,OAAO,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,KAAK,EAAE;QAC/C,QAAQ,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,MAAM,EAAE;QACjD,OAAO,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,KAAK,EAAE;QAC/C,MAAM,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,IAAI,EAAE;QAC7C,OAAO,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,KAAK,EAAE;QAC/C,SAAS,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,OAAO,EAAE;QACnD,SAAS,oBAAmB,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,OAAO,EAAE;IACrD;IAEA,IAAI,SAAS,KAAK,KAAK,aAAc,AAAC,SAAS,KAAK,GAAG,MAAO,KAAO,CAAA,SAAS,OAAO,IAAI,SAAS,OAAO,AAAD,GACtG,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,2CAA2C,CAAC;IAGzG,IAAI,SAAS,OAAO,KAAK,aAAc,AAAC,SAAS,OAAO,GAAG,MAAO,KAAM,SAAS,OAAO,EACtF,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,2CAA2C,CAAC;IAGzG,OAAO;AACT", "sources": ["packages/@internationalized/date/src/string.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyDateTime, DateTimeDuration, Disambiguation} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, possibleAbsolutes, toAbsolute, toCalendar, toCalendarDateTime, toTimeZone} from './conversion';\nimport {getLocalTimeZone} from './queries';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mu<PERSON>} from './utils';\n\nconst TIME_RE = /^(\\d{2})(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst DATE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})$/;\nconst DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst ZONED_DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:([+-]\\d{2})(?::?(\\d{2}))?)?\\[(.*?)\\]$/;\nconst ABSOLUTE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:(?:([+-]\\d{2})(?::?(\\d{2}))?)|Z)$/;\nconst DATE_TIME_DURATION_RE =\n    /^((?<negative>-)|\\+)?P((?<years>\\d*)Y)?((?<months>\\d*)M)?((?<weeks>\\d*)W)?((?<days>\\d*)D)?((?<time>T)((?<hours>\\d*[.,]?\\d{1,9})H)?((?<minutes>\\d*[.,]?\\d{1,9})M)?((?<seconds>\\d*[.,]?\\d{1,9})S)?)?$/;\nconst requiredDurationTimeGroups = ['hours', 'minutes', 'seconds'];\nconst requiredDurationGroups = ['years', 'months', 'weeks', 'days', ...requiredDurationTimeGroups];\n\n/** Parses an ISO 8601 time string. */\nexport function parseTime(value: string): Time {\n  let m = value.match(TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 time string: ' + value);\n  }\n\n  return new Time(\n    parseNumber(m[1], 0, 23),\n    m[2] ? parseNumber(m[2], 0, 59) : 0,\n    m[3] ? parseNumber(m[3], 0, 59) : 0,\n    m[4] ? parseNumber(m[4], 0, Infinity) * 1000 : 0\n  );\n}\n\n/** Parses an ISO 8601 date string, with no time components. */\nexport function parseDate(value: string): CalendarDate {\n  let m = value.match(DATE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date string: ' + value);\n  }\n\n  let date: Mutable<CalendarDate> = new CalendarDate(\n    parseNumber(m[1], 0, 9999),\n    parseNumber(m[2], 1, 12),\n    1\n  );\n\n  date.day = parseNumber(m[3], 1, date.calendar.getDaysInMonth(date));\n  return date as CalendarDate;\n}\n\n/** Parses an ISO 8601 date and time string, with no time zone. */\nexport function parseDateTime(value: string): CalendarDateTime {\n  let m = value.match(DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<CalendarDateTime> = new CalendarDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n  return date as CalendarDateTime;\n}\n\n/**\n * Parses an ISO 8601 date and time string with a time zone extension and optional UTC offset\n * (e.g. \"2021-11-07T00:45[America/Los_Angeles]\" or \"2021-11-07T00:45-07:00[America/Los_Angeles]\").\n * Ambiguous times due to daylight saving time transitions are resolved according to the `disambiguation`\n * parameter.\n */\nexport function parseZonedDateTime(value: string, disambiguation?: Disambiguation): ZonedDateTime {\n  let m = value.match(ZONED_DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[10],\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  let plainDateTime = toCalendarDateTime(date as ZonedDateTime);\n\n  let ms: number;\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n    ms = epochFromDate(date as ZonedDateTime) - date.offset;\n\n    // Validate offset against parsed date.\n    let absolutes = possibleAbsolutes(plainDateTime, date.timeZone);\n    if (!absolutes.includes(ms)) {\n      throw new Error(`Offset ${offsetToString(date.offset)} is invalid for ${dateTimeToString(date)} in ${date.timeZone}`);\n    }\n  } else {\n    // Convert to absolute and back to fix invalid times due to DST.\n    ms = toAbsolute(toCalendarDateTime(plainDateTime), date.timeZone, disambiguation);\n  }\n\n  return fromAbsolute(ms, date.timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the provided time zone.\n */\nexport function parseAbsolute(value: string, timeZone: string): ZonedDateTime {\n  let m = value.match(ABSOLUTE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    timeZone,\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n  }\n\n  return toTimeZone(date as ZonedDateTime, timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the user's local time zone.\n */\nexport function parseAbsoluteToLocal(value: string): ZonedDateTime {\n  return parseAbsolute(value, getLocalTimeZone());\n}\n\nfunction parseNumber(value: string, min: number, max: number) {\n  let val = Number(value);\n  if (val < min || val > max) {\n    throw new RangeError(`Value out of range: ${min} <= ${val} <= ${max}`);\n  }\n\n  return val;\n}\n\nexport function timeToString(time: Time): string {\n  return `${String(time.hour).padStart(2, '0')}:${String(time.minute).padStart(2, '0')}:${String(time.second).padStart(2, '0')}${time.millisecond ? String(time.millisecond / 1000).slice(1) : ''}`;\n}\n\nexport function dateToString(date: CalendarDate): string {\n  let gregorianDate = toCalendar(date, new GregorianCalendar());\n  let year: string;\n  if (gregorianDate.era === 'BC') {\n    year = gregorianDate.year === 1\n      ? '0000'\n      : '-' + String(Math.abs(1 - gregorianDate.year)).padStart(6, '00');\n  } else {\n    year = String(gregorianDate.year).padStart(4, '0');\n  }\n  return `${year}-${String(gregorianDate.month).padStart(2, '0')}-${String(gregorianDate.day).padStart(2, '0')}`;\n}\n\nexport function dateTimeToString(date: AnyDateTime): string {\n  // @ts-ignore\n  return `${dateToString(date)}T${timeToString(date)}`;\n}\n\nfunction offsetToString(offset: number) {\n  let sign = Math.sign(offset) < 0 ? '-' : '+';\n  offset = Math.abs(offset);\n  let offsetHours = Math.floor(offset / (60 * 60 * 1000));\n  let offsetMinutes = (offset % (60 * 60 * 1000)) / (60 * 1000);\n  return `${sign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;\n}\n\nexport function zonedDateTimeToString(date: ZonedDateTime): string {\n  return `${dateTimeToString(date)}${offsetToString(date.offset)}[${date.timeZone}]`;\n}\n\n/**\n * Parses an ISO 8601 duration string (e.g. \"P3Y6M6W4DT12H30M5S\").\n * @param value An ISO 8601 duration string.\n * @returns A DateTimeDuration object.\n */\nexport function parseDuration(value: string): Required<DateTimeDuration> {\n  const match = value.match(DATE_TIME_DURATION_RE);\n\n  if (!match) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const parseDurationGroup = (\n    group: string | undefined,\n    isNegative: boolean\n  ): number => {\n    if (!group) {\n      return 0;\n    }\n    try {\n      const sign = isNegative ? -1 : 1;\n      return sign * Number(group.replace(',', '.'));\n    } catch {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  };\n\n  const isNegative = !!match.groups?.negative;\n\n  const hasRequiredGroups = requiredDurationGroups.some(group => match.groups?.[group]);\n\n  if (!hasRequiredGroups) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const durationStringIncludesTime = match.groups?.time;\n\n  if (durationStringIncludesTime) {\n    const hasRequiredDurationTimeGroups = requiredDurationTimeGroups.some(group => match.groups?.[group]);\n    if (!hasRequiredDurationTimeGroups) {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  }\n\n  const duration: Mutable<DateTimeDuration> = {\n    years: parseDurationGroup(match.groups?.years, isNegative),\n    months: parseDurationGroup(match.groups?.months, isNegative),\n    weeks: parseDurationGroup(match.groups?.weeks, isNegative),\n    days: parseDurationGroup(match.groups?.days, isNegative),\n    hours: parseDurationGroup(match.groups?.hours, isNegative),\n    minutes: parseDurationGroup(match.groups?.minutes, isNegative),\n    seconds: parseDurationGroup(match.groups?.seconds, isNegative)\n  };\n\n  if (duration.hours !== undefined && ((duration.hours % 1) !== 0) && (duration.minutes || duration.seconds)) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  if (duration.minutes !== undefined && ((duration.minutes % 1) !== 0) && duration.seconds) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  return duration as Required<DateTimeDuration>;\n}\n"], "names": [], "version": 3, "file": "string.module.js.map"}