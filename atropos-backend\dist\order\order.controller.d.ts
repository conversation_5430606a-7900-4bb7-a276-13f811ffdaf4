import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderStatus, PaymentStatus, OrderType } from '../../generated/prisma';
export declare class OrderController {
    private readonly orderService;
    constructor(orderService: OrderService);
    create(createOrderDto: CreateOrderDto): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("generated/prisma/runtime/library").Decimal | null;
            productId: string;
            status: import("../../generated/prisma").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("generated/prisma/runtime/library").Decimal;
            discountRate: import("generated/prisma/runtime/library").Decimal;
            taxAmount: import("generated/prisma/runtime/library").Decimal;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("generated/prisma/runtime/library").Decimal;
            unitPrice: import("generated/prisma/runtime/library").Decimal;
            taxRate: import("generated/prisma/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import("../../generated/prisma").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import("../../generated/prisma").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        paidAmount: import("generated/prisma/runtime/library").Decimal;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        roundingAmount: import("generated/prisma/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
    findAll(branchId?: string, tableId?: string, customerId?: string, status?: OrderStatus, paymentStatus?: PaymentStatus, orderType?: OrderType, startDate?: string, endDate?: string): Promise<({
        branch: {
            name: string;
            id: string;
        };
        table: {
            number: string;
            id: string;
        } | null;
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
        waiter: {
            id: string;
            firstName: string;
            lastName: string;
        } | null;
        items: ({
            product: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                companyId: string;
                code: string;
                active: boolean;
                version: number;
                description: string | null;
                image: string | null;
                preparationTime: number | null;
                displayOrder: number;
                showInMenu: boolean;
                categoryId: string;
                taxId: string;
                barcode: string | null;
                shortDescription: string | null;
                images: string[];
                basePrice: import("generated/prisma/runtime/library").Decimal;
                costPrice: import("generated/prisma/runtime/library").Decimal | null;
                profitMargin: import("generated/prisma/runtime/library").Decimal | null;
                trackStock: boolean;
                unit: import("../../generated/prisma").$Enums.ProductUnit;
                criticalStock: import("generated/prisma/runtime/library").Decimal | null;
                available: boolean;
                sellable: boolean;
                calories: number | null;
                allergens: string[];
                hasVariants: boolean;
                hasModifiers: boolean;
                featured: boolean;
                syncId: string | null;
                lastSyncAt: Date | null;
            };
            variant: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                code: string;
                active: boolean;
                version: number;
                displayOrder: number;
                barcode: string | null;
                costPrice: import("generated/prisma/runtime/library").Decimal | null;
                productId: string;
                sku: string | null;
                price: import("generated/prisma/runtime/library").Decimal;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("generated/prisma/runtime/library").Decimal | null;
            productId: string;
            status: import("../../generated/prisma").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("generated/prisma/runtime/library").Decimal;
            discountRate: import("generated/prisma/runtime/library").Decimal;
            taxAmount: import("generated/prisma/runtime/library").Decimal;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("generated/prisma/runtime/library").Decimal;
            unitPrice: import("generated/prisma/runtime/library").Decimal;
            taxRate: import("generated/prisma/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import("../../generated/prisma").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import("../../generated/prisma").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        paidAmount: import("generated/prisma/runtime/library").Decimal;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        roundingAmount: import("generated/prisma/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        table: {
            number: string;
            id: string;
        } | null;
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
        onlinePlatform: {
            name: string;
            id: string;
        } | null;
        waiter: {
            id: string;
            firstName: string;
            lastName: string;
        } | null;
        courier: {
            id: string;
            firstName: string;
            lastName: string;
        } | null;
        items: ({
            product: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                companyId: string;
                code: string;
                active: boolean;
                version: number;
                description: string | null;
                image: string | null;
                preparationTime: number | null;
                displayOrder: number;
                showInMenu: boolean;
                categoryId: string;
                taxId: string;
                barcode: string | null;
                shortDescription: string | null;
                images: string[];
                basePrice: import("generated/prisma/runtime/library").Decimal;
                costPrice: import("generated/prisma/runtime/library").Decimal | null;
                profitMargin: import("generated/prisma/runtime/library").Decimal | null;
                trackStock: boolean;
                unit: import("../../generated/prisma").$Enums.ProductUnit;
                criticalStock: import("generated/prisma/runtime/library").Decimal | null;
                available: boolean;
                sellable: boolean;
                calories: number | null;
                allergens: string[];
                hasVariants: boolean;
                hasModifiers: boolean;
                featured: boolean;
                syncId: string | null;
                lastSyncAt: Date | null;
            };
            variant: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                code: string;
                active: boolean;
                version: number;
                displayOrder: number;
                barcode: string | null;
                costPrice: import("generated/prisma/runtime/library").Decimal | null;
                productId: string;
                sku: string | null;
                price: import("generated/prisma/runtime/library").Decimal;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("generated/prisma/runtime/library").Decimal | null;
            productId: string;
            status: import("../../generated/prisma").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("generated/prisma/runtime/library").Decimal;
            discountRate: import("generated/prisma/runtime/library").Decimal;
            taxAmount: import("generated/prisma/runtime/library").Decimal;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("generated/prisma/runtime/library").Decimal;
            unitPrice: import("generated/prisma/runtime/library").Decimal;
            taxRate: import("generated/prisma/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        })[];
        payments: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            status: import("../../generated/prisma").$Enums.PaymentStatus;
            changeAmount: import("generated/prisma/runtime/library").Decimal;
            tipAmount: import("generated/prisma/runtime/library").Decimal;
            orderId: string;
            paymentMethodId: string;
            amount: import("generated/prisma/runtime/library").Decimal;
            approvalCode: string | null;
            referenceNo: string | null;
            maskedCardNumber: string | null;
            cardHolderName: string | null;
            installments: number;
            transactionId: string | null;
            refundAmount: import("generated/prisma/runtime/library").Decimal | null;
            refundReason: string | null;
            refundedAt: Date | null;
            gatewayResponse: import("generated/prisma/runtime/library").JsonValue | null;
            paidAt: Date;
            cashMovementId: string | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import("../../generated/prisma").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import("../../generated/prisma").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        paidAmount: import("generated/prisma/runtime/library").Decimal;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        roundingAmount: import("generated/prisma/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
    update(id: string, updateOrderDto: UpdateOrderDto): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("generated/prisma/runtime/library").Decimal | null;
            productId: string;
            status: import("../../generated/prisma").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("generated/prisma/runtime/library").Decimal;
            discountRate: import("generated/prisma/runtime/library").Decimal;
            taxAmount: import("generated/prisma/runtime/library").Decimal;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("generated/prisma/runtime/library").Decimal;
            unitPrice: import("generated/prisma/runtime/library").Decimal;
            taxRate: import("generated/prisma/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import("../../generated/prisma").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import("../../generated/prisma").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        paidAmount: import("generated/prisma/runtime/library").Decimal;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        roundingAmount: import("generated/prisma/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import("../../generated/prisma").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import("../../generated/prisma").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        paidAmount: import("generated/prisma/runtime/library").Decimal;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        roundingAmount: import("generated/prisma/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
}
