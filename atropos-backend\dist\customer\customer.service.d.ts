import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
export declare class CustomerService {
    private prisma;
    constructor(prisma: PrismaService);
    createCustomer(data: CreateCustomerDto): Promise<{
        taxNumber: string | null;
        taxOffice: string | null;
        address: string | null;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        firstName: string | null;
        lastName: string | null;
        birthDate: Date | null;
        version: number;
        companyName: string | null;
        title: string | null;
        phone2: string | null;
        district: string | null;
        city: string | null;
        country: string | null;
        postalCode: string | null;
        gender: string | null;
        marketingConsent: boolean;
        smsConsent: boolean;
        emailConsent: boolean;
        loyaltyPoints: number;
        totalSpent: import("generated/prisma/runtime/library").Decimal;
        orderCount: number;
        lastOrderDate: Date | null;
        currentDebt: import("generated/prisma/runtime/library").Decimal;
        creditLimit: import("generated/prisma/runtime/library").Decimal;
        paymentTerm: number | null;
        segment: string | null;
        tags: string[];
        notes: string | null;
        source: string | null;
        referredBy: string | null;
        blacklisted: boolean;
        blacklistReason: string | null;
        customFields: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    findAllCustomers(phone?: string, email?: string, taxNumber?: string): Promise<{
        taxNumber: string | null;
        taxOffice: string | null;
        address: string | null;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        firstName: string | null;
        lastName: string | null;
        birthDate: Date | null;
        version: number;
        companyName: string | null;
        title: string | null;
        phone2: string | null;
        district: string | null;
        city: string | null;
        country: string | null;
        postalCode: string | null;
        gender: string | null;
        marketingConsent: boolean;
        smsConsent: boolean;
        emailConsent: boolean;
        loyaltyPoints: number;
        totalSpent: import("generated/prisma/runtime/library").Decimal;
        orderCount: number;
        lastOrderDate: Date | null;
        currentDebt: import("generated/prisma/runtime/library").Decimal;
        creditLimit: import("generated/prisma/runtime/library").Decimal;
        paymentTerm: number | null;
        segment: string | null;
        tags: string[];
        notes: string | null;
        source: string | null;
        referredBy: string | null;
        blacklisted: boolean;
        blacklistReason: string | null;
        customFields: import("generated/prisma/runtime/library").JsonValue | null;
    }[]>;
    findOneCustomer(id: string): Promise<{
        taxNumber: string | null;
        taxOffice: string | null;
        address: string | null;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        firstName: string | null;
        lastName: string | null;
        birthDate: Date | null;
        version: number;
        companyName: string | null;
        title: string | null;
        phone2: string | null;
        district: string | null;
        city: string | null;
        country: string | null;
        postalCode: string | null;
        gender: string | null;
        marketingConsent: boolean;
        smsConsent: boolean;
        emailConsent: boolean;
        loyaltyPoints: number;
        totalSpent: import("generated/prisma/runtime/library").Decimal;
        orderCount: number;
        lastOrderDate: Date | null;
        currentDebt: import("generated/prisma/runtime/library").Decimal;
        creditLimit: import("generated/prisma/runtime/library").Decimal;
        paymentTerm: number | null;
        segment: string | null;
        tags: string[];
        notes: string | null;
        source: string | null;
        referredBy: string | null;
        blacklisted: boolean;
        blacklistReason: string | null;
        customFields: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    updateCustomer(id: string, data: UpdateCustomerDto): Promise<{
        taxNumber: string | null;
        taxOffice: string | null;
        address: string | null;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        firstName: string | null;
        lastName: string | null;
        birthDate: Date | null;
        version: number;
        companyName: string | null;
        title: string | null;
        phone2: string | null;
        district: string | null;
        city: string | null;
        country: string | null;
        postalCode: string | null;
        gender: string | null;
        marketingConsent: boolean;
        smsConsent: boolean;
        emailConsent: boolean;
        loyaltyPoints: number;
        totalSpent: import("generated/prisma/runtime/library").Decimal;
        orderCount: number;
        lastOrderDate: Date | null;
        currentDebt: import("generated/prisma/runtime/library").Decimal;
        creditLimit: import("generated/prisma/runtime/library").Decimal;
        paymentTerm: number | null;
        segment: string | null;
        tags: string[];
        notes: string | null;
        source: string | null;
        referredBy: string | null;
        blacklisted: boolean;
        blacklistReason: string | null;
        customFields: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    removeCustomer(id: string): Promise<{
        taxNumber: string | null;
        taxOffice: string | null;
        address: string | null;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        firstName: string | null;
        lastName: string | null;
        birthDate: Date | null;
        version: number;
        companyName: string | null;
        title: string | null;
        phone2: string | null;
        district: string | null;
        city: string | null;
        country: string | null;
        postalCode: string | null;
        gender: string | null;
        marketingConsent: boolean;
        smsConsent: boolean;
        emailConsent: boolean;
        loyaltyPoints: number;
        totalSpent: import("generated/prisma/runtime/library").Decimal;
        orderCount: number;
        lastOrderDate: Date | null;
        currentDebt: import("generated/prisma/runtime/library").Decimal;
        creditLimit: import("generated/prisma/runtime/library").Decimal;
        paymentTerm: number | null;
        segment: string | null;
        tags: string[];
        notes: string | null;
        source: string | null;
        referredBy: string | null;
        blacklisted: boolean;
        blacklistReason: string | null;
        customFields: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
}
