import { TableAreaService } from './table-area.service';
import { CreateTableAreaDto } from './dto/create-table-area.dto';
import { UpdateTableAreaDto } from './dto/update-table-area.dto';
export declare class TableAreaController {
    private readonly tableAreaService;
    constructor(tableAreaService: TableAreaService);
    create(createTableAreaDto: CreateTableAreaDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
    findAll(branchId?: string): Promise<({
        branch: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
    update(id: string, updateTableAreaDto: UpdateTableAreaDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
}
