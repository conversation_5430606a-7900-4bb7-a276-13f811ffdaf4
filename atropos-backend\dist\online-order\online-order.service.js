"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineOrderService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
var OnlineOrderStatus;
(function (OnlineOrderStatus) {
    OnlineOrderStatus["PENDING"] = "PENDING";
    OnlineOrderStatus["ACCEPTED"] = "ACCEPTED";
    OnlineOrderStatus["REJECTED"] = "REJECTED";
    OnlineOrderStatus["PREPARING"] = "PREPARING";
    OnlineOrderStatus["READY"] = "READY";
    OnlineOrderStatus["DELIVERING"] = "DELIVERING";
    OnlineOrderStatus["DELIVERED"] = "DELIVERED";
    OnlineOrderStatus["CANCELLED"] = "CANCELLED";
    OnlineOrderStatus["RETURNED"] = "RETURNED";
})(OnlineOrderStatus || (OnlineOrderStatus = {}));
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "PENDING";
    OrderStatus["CONFIRMED"] = "CONFIRMED";
    OrderStatus["PREPARING"] = "PREPARING";
    OrderStatus["READY"] = "READY";
    OrderStatus["DELIVERED"] = "DELIVERED";
    OrderStatus["COMPLETED"] = "COMPLETED";
    OrderStatus["CANCELLED"] = "CANCELLED";
})(OrderStatus || (OrderStatus = {}));
let OnlineOrderService = class OnlineOrderService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createOnlineOrder(data) {
        const platform = await this.prisma.onlinePlatform.findUnique({
            where: { id: data.platformId },
        });
        if (!platform) {
            throw new common_1.NotFoundException(`Online platform with ID "${data.platformId}" not found.`);
        }
        const existingOnlineOrder = await this.prisma.onlineOrder.findUnique({
            where: {
                platformId_platformOrderId: {
                    platformId: data.platformId,
                    platformOrderId: data.platformOrderId,
                },
            },
        });
        if (existingOnlineOrder) {
            throw new common_1.ConflictException(`Online order with platform order ID "${data.platformOrderId}" already exists for this platform.`);
        }
        if (data.orderId) {
            const posOrderExists = await this.prisma.order.findUnique({
                where: { id: data.orderId, deletedAt: null }
            });
            if (!posOrderExists) {
                throw new common_1.NotFoundException(`POS Order with ID "${data.orderId}" not found.`);
            }
            const existingMappingToPosOrder = await this.prisma.onlineOrder.findUnique({
                where: { orderId: data.orderId }
            });
            if (existingMappingToPosOrder) {
                throw new common_1.ConflictException(`POS Order with ID "${data.orderId}" is already linked to another online order.`);
            }
        }
        const onlineOrder = await this.prisma.onlineOrder.create({
            data: {
                platformId: data.platformId,
                orderId: data.orderId,
                platformOrderId: data.platformOrderId,
                platformOrderNo: data.platformOrderNo,
                customerName: data.customerName,
                customerPhone: data.phone,
                customerEmail: data.customerEmail,
                deliveryAddress: data.deliveryAddress,
                deliveryNote: data.deliveryNote,
                orderData: data.orderData || {},
                status: data.status || OnlineOrderStatus.PENDING,
                platformStatus: data.platformStatus,
                subtotal: parseFloat(data.subtotal.toFixed(2)),
                deliveryFee: data.deliveryFee !== undefined ? parseFloat(data.deliveryFee.toFixed(2)) : 0,
                serviceFee: data.serviceFee !== undefined ? parseFloat(data.serviceFee.toFixed(2)) : 0,
                discount: data.discount !== undefined ? parseFloat(data.discount.toFixed(2)) : 0,
                totalAmount: parseFloat(data.totalAmount.toFixed(2)),
                commissionAmount: data.commissionAmount !== undefined ? parseFloat(data.commissionAmount.toFixed(2)) : 0,
                netAmount: data.netAmount !== undefined ? parseFloat(data.netAmount.toFixed(2)) : 0,
                paymentMethod: data.paymentMethod,
                isPaid: data.isPaid || false,
                orderedAt: data.orderedAt,
                requestedAt: data.requestedAt,
                rejectReason: data.rejectReason,
                cancelReason: data.cancelReason,
            },
        });
        if (onlineOrder.orderId) {
            await this.prisma.order.update({
                where: { id: onlineOrder.orderId },
                data: { onlineOrder: { connect: { id: onlineOrder.id } } }
            });
        }
        return onlineOrder;
    }
    async findAllOnlineOrders(platformId, orderId, status, platformOrderNo, startDate, endDate) {
        return this.prisma.onlineOrder.findMany({
            where: {
                platformId: platformId || undefined,
                orderId: orderId || undefined,
                status: status || undefined,
                platformOrderNo: platformOrderNo || undefined,
                orderedAt: {
                    gte: startDate || undefined,
                    lte: endDate || undefined,
                },
            },
            include: {
                platform: { select: { id: true, name: true, code: true } },
                order: { select: { id: true, orderNumber: true, totalAmount: true, status: true } },
            },
            orderBy: { orderedAt: 'desc' },
        });
    }
    async findOneOnlineOrder(id) {
        const onlineOrder = await this.prisma.onlineOrder.findUnique({
            where: { id },
            include: {
                platform: { select: { id: true, name: true, code: true } },
                order: { select: { id: true, orderNumber: true, totalAmount: true, status: true } },
            },
        });
        if (!onlineOrder) {
            throw new common_1.NotFoundException(`Online order with ID "${id}" not found.`);
        }
        return onlineOrder;
    }
    async updateOnlineOrder(id, data) {
        const existingOnlineOrder = await this.findOneOnlineOrder(id);
        try {
            const updatedOnlineOrder = await this.prisma.onlineOrder.update({
                where: { id },
                data: {
                    status: data.status,
                    platformStatus: data.platformStatus,
                    acceptedAt: data.acceptedAt,
                    rejectedAt: data.rejectedAt,
                    preparingAt: data.preparingAt,
                    readyAt: data.readyAt,
                    deliveringAt: data.deliveringAt,
                    deliveredAt: data.deliveredAt,
                    cancelledAt: data.cancelledAt,
                    rejectReason: data.rejectReason,
                    cancelReason: data.cancelReason,
                    isPaid: data.isPaid,
                    orderData: data.orderData,
                },
            });
            if (data.status && updatedOnlineOrder.orderId) {
                let posOrderStatus;
                switch (data.status) {
                    case OnlineOrderStatus.ACCEPTED:
                        posOrderStatus = OrderStatus.CONFIRMED;
                        break;
                    case OnlineOrderStatus.PREPARING:
                        posOrderStatus = OrderStatus.PREPARING;
                        break;
                    case OnlineOrderStatus.READY:
                        posOrderStatus = OrderStatus.READY;
                        break;
                    case OnlineOrderStatus.DELIVERING:
                        posOrderStatus = OrderStatus.DELIVERED;
                        break;
                    case OnlineOrderStatus.DELIVERED:
                        posOrderStatus = OrderStatus.COMPLETED;
                        break;
                    case OnlineOrderStatus.CANCELLED:
                        posOrderStatus = OrderStatus.CANCELLED;
                        break;
                    case OnlineOrderStatus.REJECTED:
                        posOrderStatus = OrderStatus.CANCELLED;
                        break;
                }
                if (posOrderStatus) {
                    await this.prisma.order.update({
                        where: { id: updatedOnlineOrder.orderId },
                        data: { status: posOrderStatus }
                    });
                }
            }
            return updatedOnlineOrder;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Online order with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeOnlineOrder(id) {
        try {
            const onlineOrder = await this.prisma.onlineOrder.delete({
                where: { id },
            });
            if (onlineOrder.orderId) {
                await this.prisma.order.update({
                    where: { id: onlineOrder.orderId },
                    data: { onlineOrder: { disconnect: true } }
                });
            }
            return onlineOrder;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Online order with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.OnlineOrderService = OnlineOrderService;
exports.OnlineOrderService = OnlineOrderService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OnlineOrderService);
//# sourceMappingURL=online-order.service.js.map