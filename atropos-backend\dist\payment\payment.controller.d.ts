import { PaymentService } from './payment.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { RefundPaymentDto } from './dto/update-payment.dto';
import { PaymentStatus } from '../../generated/prisma';
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    create(createPaymentDto: CreatePaymentDto): Promise<{
        order: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            branchId: string;
            version: number;
            syncId: string | null;
            lastSyncAt: Date | null;
            status: import("../../generated/prisma").$Enums.OrderStatus;
            completedAt: Date | null;
            orderNumber: string;
            orderCode: string | null;
            orderType: import("../../generated/prisma").$Enums.OrderType;
            tableId: string | null;
            customerCount: number | null;
            customerId: string | null;
            customerName: string | null;
            customerPhone: string | null;
            deliveryAddress: string | null;
            deliveryNote: string | null;
            paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
            mergeTargetId: string | null;
            splitFromId: string | null;
            subtotal: import("generated/prisma/runtime/library").Decimal;
            discountAmount: import("generated/prisma/runtime/library").Decimal;
            discountRate: import("generated/prisma/runtime/library").Decimal;
            discountReason: string | null;
            serviceCharge: import("generated/prisma/runtime/library").Decimal;
            deliveryFee: import("generated/prisma/runtime/library").Decimal;
            taxAmount: import("generated/prisma/runtime/library").Decimal;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
            paidAmount: import("generated/prisma/runtime/library").Decimal;
            changeAmount: import("generated/prisma/runtime/library").Decimal;
            tipAmount: import("generated/prisma/runtime/library").Decimal;
            roundingAmount: import("generated/prisma/runtime/library").Decimal;
            waiterId: string | null;
            cashierId: string | null;
            courierId: string | null;
            orderNote: string | null;
            kitchenNote: string | null;
            internalNote: string | null;
            orderedAt: Date;
            confirmedAt: Date | null;
            preparingAt: Date | null;
            preparedAt: Date | null;
            servedAt: Date | null;
            deliveredAt: Date | null;
            cancelledAt: Date | null;
            estimatedTime: number | null;
            actualTime: number | null;
            onlinePlatformId: string | null;
            platformOrderId: string | null;
            platformOrderNo: string | null;
        };
        paymentMethod: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            companyId: string;
            code: string;
            active: boolean;
            version: number;
            displayOrder: number;
            type: import("../../generated/prisma").$Enums.PaymentMethodType;
            merchantId: string | null;
            commissionRate: import("generated/prisma/runtime/library").Decimal;
            minAmount: import("generated/prisma/runtime/library").Decimal | null;
            maxAmount: import("generated/prisma/runtime/library").Decimal | null;
            requiresApproval: boolean;
            requiresReference: boolean;
            providerName: string | null;
            terminalId: string | null;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        version: number;
        status: import("../../generated/prisma").$Enums.PaymentStatus;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string;
        paymentMethodId: string;
        amount: import("generated/prisma/runtime/library").Decimal;
        approvalCode: string | null;
        referenceNo: string | null;
        maskedCardNumber: string | null;
        cardHolderName: string | null;
        installments: number;
        transactionId: string | null;
        refundAmount: import("generated/prisma/runtime/library").Decimal | null;
        refundReason: string | null;
        refundedAt: Date | null;
        gatewayResponse: import("generated/prisma/runtime/library").JsonValue | null;
        paidAt: Date;
        cashMovementId: string | null;
    }>;
    findAll(orderId?: string, paymentMethodId?: string, status?: PaymentStatus): Promise<({
        order: {
            id: string;
            orderNumber: string;
            paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        };
        paymentMethod: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        version: number;
        status: import("../../generated/prisma").$Enums.PaymentStatus;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string;
        paymentMethodId: string;
        amount: import("generated/prisma/runtime/library").Decimal;
        approvalCode: string | null;
        referenceNo: string | null;
        maskedCardNumber: string | null;
        cardHolderName: string | null;
        installments: number;
        transactionId: string | null;
        refundAmount: import("generated/prisma/runtime/library").Decimal | null;
        refundReason: string | null;
        refundedAt: Date | null;
        gatewayResponse: import("generated/prisma/runtime/library").JsonValue | null;
        paidAt: Date;
        cashMovementId: string | null;
    })[]>;
    findOne(id: string): Promise<{
        order: {
            id: string;
            orderNumber: string;
            paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        };
        paymentMethod: {
            name: string;
            id: string;
            code: string;
        };
        cashMovement: {
            id: string;
            createdAt: Date;
            cashRegisterId: string | null;
            branchId: string;
            description: string;
            type: import("../../generated/prisma").$Enums.CashMovementType;
            paymentMethodId: string | null;
            amount: import("generated/prisma/runtime/library").Decimal;
            userId: string;
            referenceId: string | null;
            referenceType: string | null;
            previousBalance: import("generated/prisma/runtime/library").Decimal;
            currentBalance: import("generated/prisma/runtime/library").Decimal;
            safeId: string | null;
            approvedBy: string | null;
            approvedAt: Date | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        version: number;
        status: import("../../generated/prisma").$Enums.PaymentStatus;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string;
        paymentMethodId: string;
        amount: import("generated/prisma/runtime/library").Decimal;
        approvalCode: string | null;
        referenceNo: string | null;
        maskedCardNumber: string | null;
        cardHolderName: string | null;
        installments: number;
        transactionId: string | null;
        refundAmount: import("generated/prisma/runtime/library").Decimal | null;
        refundReason: string | null;
        refundedAt: Date | null;
        gatewayResponse: import("generated/prisma/runtime/library").JsonValue | null;
        paidAt: Date;
        cashMovementId: string | null;
    }>;
    refund(id: string, refundDto: RefundPaymentDto): Promise<{
        order: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            branchId: string;
            version: number;
            syncId: string | null;
            lastSyncAt: Date | null;
            status: import("../../generated/prisma").$Enums.OrderStatus;
            completedAt: Date | null;
            orderNumber: string;
            orderCode: string | null;
            orderType: import("../../generated/prisma").$Enums.OrderType;
            tableId: string | null;
            customerCount: number | null;
            customerId: string | null;
            customerName: string | null;
            customerPhone: string | null;
            deliveryAddress: string | null;
            deliveryNote: string | null;
            paymentStatus: import("../../generated/prisma").$Enums.PaymentStatus;
            mergeTargetId: string | null;
            splitFromId: string | null;
            subtotal: import("generated/prisma/runtime/library").Decimal;
            discountAmount: import("generated/prisma/runtime/library").Decimal;
            discountRate: import("generated/prisma/runtime/library").Decimal;
            discountReason: string | null;
            serviceCharge: import("generated/prisma/runtime/library").Decimal;
            deliveryFee: import("generated/prisma/runtime/library").Decimal;
            taxAmount: import("generated/prisma/runtime/library").Decimal;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
            paidAmount: import("generated/prisma/runtime/library").Decimal;
            changeAmount: import("generated/prisma/runtime/library").Decimal;
            tipAmount: import("generated/prisma/runtime/library").Decimal;
            roundingAmount: import("generated/prisma/runtime/library").Decimal;
            waiterId: string | null;
            cashierId: string | null;
            courierId: string | null;
            orderNote: string | null;
            kitchenNote: string | null;
            internalNote: string | null;
            orderedAt: Date;
            confirmedAt: Date | null;
            preparingAt: Date | null;
            preparedAt: Date | null;
            servedAt: Date | null;
            deliveredAt: Date | null;
            cancelledAt: Date | null;
            estimatedTime: number | null;
            actualTime: number | null;
            onlinePlatformId: string | null;
            platformOrderId: string | null;
            platformOrderNo: string | null;
        };
        paymentMethod: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            companyId: string;
            code: string;
            active: boolean;
            version: number;
            displayOrder: number;
            type: import("../../generated/prisma").$Enums.PaymentMethodType;
            merchantId: string | null;
            commissionRate: import("generated/prisma/runtime/library").Decimal;
            minAmount: import("generated/prisma/runtime/library").Decimal | null;
            maxAmount: import("generated/prisma/runtime/library").Decimal | null;
            requiresApproval: boolean;
            requiresReference: boolean;
            providerName: string | null;
            terminalId: string | null;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        version: number;
        status: import("../../generated/prisma").$Enums.PaymentStatus;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string;
        paymentMethodId: string;
        amount: import("generated/prisma/runtime/library").Decimal;
        approvalCode: string | null;
        referenceNo: string | null;
        maskedCardNumber: string | null;
        cardHolderName: string | null;
        installments: number;
        transactionId: string | null;
        refundAmount: import("generated/prisma/runtime/library").Decimal | null;
        refundReason: string | null;
        refundedAt: Date | null;
        gatewayResponse: import("generated/prisma/runtime/library").JsonValue | null;
        paidAt: Date;
        cashMovementId: string | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        version: number;
        status: import("../../generated/prisma").$Enums.PaymentStatus;
        changeAmount: import("generated/prisma/runtime/library").Decimal;
        tipAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string;
        paymentMethodId: string;
        amount: import("generated/prisma/runtime/library").Decimal;
        approvalCode: string | null;
        referenceNo: string | null;
        maskedCardNumber: string | null;
        cardHolderName: string | null;
        installments: number;
        transactionId: string | null;
        refundAmount: import("generated/prisma/runtime/library").Decimal | null;
        refundReason: string | null;
        refundedAt: Date | null;
        gatewayResponse: import("generated/prisma/runtime/library").JsonValue | null;
        paidAt: Date;
        cashMovementId: string | null;
    }>;
}
