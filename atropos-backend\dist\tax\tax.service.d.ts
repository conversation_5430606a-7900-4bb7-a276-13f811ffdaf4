import { PrismaService } from '../prisma/prisma.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
export declare class TaxService {
    private prisma;
    constructor(prisma: PrismaService);
    createTax(data: CreateTaxDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
    findAllTaxes(companyId?: string): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    })[]>;
    findOneTax(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
    updateTax(id: string, data: UpdateTaxDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
    removeTax(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
}
