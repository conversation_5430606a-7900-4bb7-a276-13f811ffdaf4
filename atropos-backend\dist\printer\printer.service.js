"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrinterService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
var PrinterType;
(function (PrinterType) {
    PrinterType["THERMAL"] = "THERMAL";
    PrinterType["DOT_MATRIX"] = "DOT_MATRIX";
    PrinterType["A4"] = "A4";
})(PrinterType || (PrinterType = {}));
let PrinterService = class PrinterService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createPrinter(data) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        if (data.printerGroupId) {
            const printerGroupExists = await this.prisma.printerGroup.findUnique({
                where: { id: data.printerGroupId },
            });
            if (!printerGroupExists) {
                throw new common_1.NotFoundException(`Printer group with ID "${data.printerGroupId}" not found.`);
            }
        }
        if (data.connectionType === 'NETWORK' && !data.ipAddress) {
            throw new common_1.BadRequestException('IP Address is required for NETWORK connection type.');
        }
        const existingPrinterByName = await this.prisma.printer.findFirst({
            where: { branchId: data.branchId, name: data.name },
        });
        if (existingPrinterByName) {
            throw new common_1.ConflictException(`Printer with name "${data.name}" already exists for this branch.`);
        }
        if (data.connectionType === 'NETWORK' && data.ipAddress && data.port) {
            const existingNetworkPrinter = await this.prisma.printer.findFirst({
                where: {
                    branchId: data.branchId,
                    connectionType: 'NETWORK',
                    ipAddress: data.ipAddress,
                    port: data.port,
                },
            });
            if (existingNetworkPrinter) {
                throw new common_1.ConflictException(`Network printer with IP "${data.ipAddress}" and port "${data.port}" already exists for this branch.`);
            }
        }
        return this.prisma.printer.create({ data });
    }
    async findAllPrinters(branchId, printerGroupId, type, active) {
        return this.prisma.printer.findMany({
            where: {
                branchId: branchId || undefined,
                printerGroupId: printerGroupId || undefined,
                type: type || undefined,
                active: active !== undefined ? active : undefined,
            },
            include: {
                branch: { select: { id: true, name: true } },
                printerGroup: { select: { id: true, name: true } },
            },
            orderBy: { name: 'asc' },
        });
    }
    async findOnePrinter(id) {
        const printer = await this.prisma.printer.findUnique({
            where: { id },
            include: {
                branch: { select: { id: true, name: true } },
                printerGroup: { select: { id: true, name: true } },
            },
        });
        if (!printer) {
            throw new common_1.NotFoundException(`Printer with ID "${id}" not found.`);
        }
        return printer;
    }
    async updatePrinter(id, data) {
        const existingPrinter = await this.findOnePrinter(id);
        if (data.branchId && data.branchId !== existingPrinter.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists) {
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
            }
        }
        if (data.printerGroupId && data.printerGroupId !== existingPrinter.printerGroupId) {
            const printerGroupExists = await this.prisma.printerGroup.findUnique({ where: { id: data.printerGroupId } });
            if (!printerGroupExists) {
                throw new common_1.NotFoundException(`Printer group with ID "${data.printerGroupId}" not found.`);
            }
        }
        const targetConnectionType = data.connectionType || existingPrinter.connectionType;
        const targetIpAddress = data.ipAddress || existingPrinter.ipAddress;
        if (targetConnectionType === 'NETWORK' && !targetIpAddress) {
            throw new common_1.BadRequestException('IP Address is required for NETWORK connection type.');
        }
        if (data.name && data.name !== existingPrinter.name) {
            const existingPrinterByName = await this.prisma.printer.findFirst({
                where: { branchId: data.branchId || existingPrinter.branchId, name: data.name },
            });
            if (existingPrinterByName && existingPrinterByName.id !== id) {
                throw new common_1.ConflictException(`Printer with name "${data.name}" already exists for this branch.`);
            }
        }
        if (targetConnectionType === 'NETWORK' && targetIpAddress && (data.port !== undefined || existingPrinter.port !== undefined)) {
            const targetPort = data.port !== undefined ? data.port : existingPrinter.port;
            const existingNetworkPrinter = await this.prisma.printer.findFirst({
                where: {
                    branchId: data.branchId || existingPrinter.branchId,
                    connectionType: 'NETWORK',
                    ipAddress: targetIpAddress,
                    port: targetPort,
                    id: { not: id }
                },
            });
            if (existingNetworkPrinter) {
                throw new common_1.ConflictException(`Network printer with IP "${targetIpAddress}" and port "${targetPort}" already exists for this branch.`);
            }
        }
        try {
            return await this.prisma.printer.update({
                where: { id },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Printer with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removePrinter(id) {
        try {
            return await this.prisma.printer.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Printer with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.PrinterService = PrinterService;
exports.PrinterService = PrinterService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PrinterService);
//# sourceMappingURL=printer.service.js.map