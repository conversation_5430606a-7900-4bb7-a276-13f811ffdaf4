"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlinePlatformController = void 0;
const common_1 = require("@nestjs/common");
const online_platform_service_1 = require("./online-platform.service");
const create_online_platform_dto_1 = require("./dto/create-online-platform.dto");
const update_online_platform_dto_1 = require("./dto/update-online-platform.dto");
let OnlinePlatformController = class OnlinePlatformController {
    onlinePlatformService;
    constructor(onlinePlatformService) {
        this.onlinePlatformService = onlinePlatformService;
    }
    create(createOnlinePlatformDto) {
        return this.onlinePlatformService.createOnlinePlatform(createOnlinePlatformDto);
    }
    findAll(companyId) {
        return this.onlinePlatformService.findAllOnlinePlatforms(companyId);
    }
    findOne(id) {
        return this.onlinePlatformService.findOneOnlinePlatform(id);
    }
    update(id, updateOnlinePlatformDto) {
        return this.onlinePlatformService.updateOnlinePlatform(id, updateOnlinePlatformDto);
    }
    remove(id) {
        return this.onlinePlatformService.removeOnlinePlatform(id);
    }
};
exports.OnlinePlatformController = OnlinePlatformController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_online_platform_dto_1.CreateOnlinePlatformDto]),
    __metadata("design:returntype", void 0)
], OnlinePlatformController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlinePlatformController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlinePlatformController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_online_platform_dto_1.UpdateOnlinePlatformDto]),
    __metadata("design:returntype", void 0)
], OnlinePlatformController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlinePlatformController.prototype, "remove", null);
exports.OnlinePlatformController = OnlinePlatformController = __decorate([
    (0, common_1.Controller)('online-platform'),
    __metadata("design:paramtypes", [online_platform_service_1.OnlinePlatformService])
], OnlinePlatformController);
//# sourceMappingURL=online-platform.controller.js.map