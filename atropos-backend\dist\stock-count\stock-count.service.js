"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockCountService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
const stock_movement_service_1 = require("../stock-movement/stock-movement.service");
let StockCountService = class StockCountService {
    prisma;
    stockMovementService;
    constructor(prisma, stockMovementService) {
        this.prisma = prisma;
        this.stockMovementService = stockMovementService;
    }
    async processStockCountItems(items, branchId) {
        const processedItems = [];
        for (const itemDto of items) {
            const inventoryItem = await this.prisma.inventoryItem.findUnique({
                where: { id: itemDto.inventoryItemId, deletedAt: null },
            });
            if (!inventoryItem) {
                throw new common_1.NotFoundException(`Inventory item with ID "${itemDto.inventoryItemId}" not found for stock count item.`);
            }
            const systemQuantity = inventoryItem.currentStock.toNumber();
            const unitCost = inventoryItem.averageCost?.toNumber() || 0;
            const countedQuantity = parseFloat(itemDto.countedQuantity.toFixed(3));
            const difference = countedQuantity - systemQuantity;
            const totalDifference = parseFloat((difference * unitCost).toFixed(2));
            processedItems.push({
                inventoryItemId: itemDto.inventoryItemId,
                systemQuantity: parseFloat(systemQuantity.toFixed(3)),
                countedQuantity: countedQuantity,
                difference: parseFloat(difference.toFixed(3)),
                unitCost: parseFloat(unitCost.toFixed(2)),
                totalDifference: totalDifference,
                note: itemDto.note,
            });
        }
        return processedItems;
    }
    async createStockCount(data) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByExists) {
            throw new common_1.NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`);
        }
        if (data.approvedBy) {
            const approvedByExists = await this.prisma.user.findUnique({ where: { id: data.approvedBy, deletedAt: null } });
            if (!approvedByExists) {
                throw new common_1.NotFoundException(`User (approvedBy) with ID "${data.approvedBy}" not found.`);
            }
        }
        const existingCount = await this.prisma.stockCount.findFirst({
            where: {
                branchId: data.branchId,
                countDate: {
                    gte: new Date(data.countDate).toISOString().split('T')[0] + 'T00:00:00.000Z',
                    lt: new Date(data.countDate).toISOString().split('T')[0] + 'T23:59:59.999Z',
                },
            },
        });
        if (existingCount) {
            throw new common_1.ConflictException(`Stock count for branch "${data.branchId}" on ${new Date(data.countDate).toISOString().split('T')[0]} already exists.`);
        }
        const processedItems = await this.processStockCountItems(data.items, data.branchId);
        const stockCount = await this.prisma.stockCount.create({
            data: {
                ...data,
                countDate: new Date(data.countDate),
                status: data.status || prisma_1.StockCountStatus.DRAFT,
                items: {
                    create: processedItems.map(item => ({
                        inventoryItemId: item.inventoryItemId,
                        systemQuantity: item.systemQuantity,
                        countedQuantity: item.countedQuantity,
                        difference: item.difference,
                        unitCost: item.unitCost,
                        totalDifference: item.totalDifference,
                        note: item.note,
                    })),
                },
            },
            include: { items: true },
        });
        if (stockCount.status === 'APPROVED') {
            await this.applyStockCountAdjustments(stockCount.id, stockCount.branchId, stockCount.createdBy);
        }
        return stockCount;
    }
    async findAllStockCounts(branchId, countType, status, startDate, endDate) {
        return this.prisma.stockCount.findMany({
            where: {
                branchId: branchId || undefined,
                countType: countType || undefined,
                status: status || undefined,
                countDate: {
                    gte: startDate ? new Date(startDate.toISOString().split('T')[0]) : undefined,
                    lte: endDate ? new Date(endDate.toISOString().split('T')[0] + 'T23:59:59.999Z') : undefined,
                },
            },
            include: {},
            orderBy: { countDate: 'desc' },
        });
    }
    async findOneStockCount(id) {
        const stockCount = await this.prisma.stockCount.findUnique({
            where: { id },
            include: {
                items: { include: { inventoryItem: { select: { id: true, name: true, unit: true, currentStock: true, averageCost: true } } } },
            },
        });
        if (!stockCount) {
            throw new common_1.NotFoundException(`Stock count with ID "${id}" not found.`);
        }
        return stockCount;
    }
    async updateStockCount(id, data) {
        const existingStockCount = await this.findOneStockCount(id);
        if (existingStockCount.status === 'APPROVED' || existingStockCount.status === 'CANCELLED') {
            throw new common_1.BadRequestException(`Cannot update a stock count with status "${existingStockCount.status}".`);
        }
        if (data.branchId && data.branchId !== existingStockCount.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists)
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        if (data.approvedBy && data.approvedBy !== existingStockCount.approvedBy) {
            const approvedByExists = await this.prisma.user.findUnique({ where: { id: data.approvedBy, deletedAt: null } });
            if (!approvedByExists)
                throw new common_1.NotFoundException(`User (approvedBy) with ID "${data.approvedBy}" not found.`);
        }
        if (data.branchId || data.countDate) {
            const targetBranchId = data.branchId || existingStockCount.branchId;
            const targetCountDate = data.countDate ? new Date(data.countDate) : existingStockCount.countDate;
            const existingWithNewData = await this.prisma.stockCount.findFirst({
                where: {
                    branchId: targetBranchId,
                    countDate: {
                        gte: targetCountDate.toISOString().split('T')[0] + 'T00:00:00.000Z',
                        lt: targetCountDate.toISOString().split('T')[0] + 'T23:59:59.999Z',
                    },
                },
            });
            if (existingWithNewData && existingWithNewData.id !== id) {
                throw new common_1.ConflictException(`Stock count for branch "${targetBranchId}" on ${targetCountDate.toISOString().split('T')[0]} already exists.`);
            }
        }
        const transaction = [];
        let updatedItemsData = [];
        if (data.items) {
            const currentStockCountItems = await this.prisma.stockCountItem.findMany({ where: { stockCountId: id } });
            const currentItemIds = new Set(currentStockCountItems.map(item => item.id));
            const incomingItemIds = new Set(data.items.filter(item => item.id).map(item => item.id));
            const itemsToDelete = currentStockCountItems.filter(item => !incomingItemIds.has(item.id));
            if (itemsToDelete.length > 0) {
                transaction.push(this.prisma.stockCountItem.deleteMany({
                    where: { id: { in: itemsToDelete.map(item => item.id) } }
                }));
            }
            for (const item of data.items) {
                if (item.id && currentItemIds.has(item.id)) {
                    const processedItem = (await this.processStockCountItems([item], existingStockCount.branchId))[0];
                    transaction.push(this.prisma.stockCountItem.update({
                        where: { id: item.id },
                        data: {
                            countedQuantity: processedItem.countedQuantity,
                            difference: processedItem.difference,
                            totalDifference: processedItem.totalDifference,
                            note: item.note,
                        }
                    }));
                    updatedItemsData.push({ ...processedItem, id: item.id });
                }
                else {
                    const processedItem = (await this.processStockCountItems([item], existingStockCount.branchId))[0];
                    transaction.push(this.prisma.stockCountItem.create({
                        data: {
                            stockCountId: id,
                            inventoryItemId: processedItem.inventoryItemId,
                            systemQuantity: processedItem.systemQuantity,
                            countedQuantity: processedItem.countedQuantity,
                            difference: processedItem.difference,
                            unitCost: processedItem.unitCost,
                            totalDifference: processedItem.totalDifference,
                            note: processedItem.note,
                        }
                    }));
                    updatedItemsData.push(processedItem);
                }
            }
        }
        else {
            updatedItemsData = (await this.prisma.stockCountItem.findMany({ where: { stockCountId: id } })).map(item => ({
                inventoryItemId: item.inventoryItemId,
                systemQuantity: item.systemQuantity.toNumber(),
                countedQuantity: item.countedQuantity.toNumber(),
                difference: item.difference.toNumber(),
                unitCost: item.unitCost?.toNumber() || 0,
                totalDifference: item.totalDifference?.toNumber() || 0,
                note: item.note,
                id: item.id
            }));
        }
        const updatedStockCountData = {
            ...data,
            items: undefined,
            countDate: data.countDate ? new Date(data.countDate) : undefined,
            startedAt: data.startedAt || undefined,
            completedAt: data.completedAt || undefined,
            approvedAt: data.approvedAt || undefined,
        };
        transaction.push(this.prisma.stockCount.update({
            where: { id },
            data: updatedStockCountData,
        }));
        try {
            await this.prisma.$transaction(transaction);
            if (data.status === 'APPROVED' && existingStockCount.status !== 'APPROVED') {
                await this.applyStockCountAdjustments(id, existingStockCount.branchId, data.approvedBy || existingStockCount.createdBy);
            }
            return this.findOneStockCount(id);
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Stock count with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async applyStockCountAdjustments(stockCountId, branchId, userId) {
        const stockCount = await this.prisma.stockCount.findUnique({
            where: { id: stockCountId },
            include: { items: true }
        });
        if (!stockCount) {
            throw new common_1.NotFoundException(`Stock count with ID "${stockCountId}" not found for adjustment.`);
        }
        if (stockCount.status !== 'APPROVED') {
            throw new common_1.BadRequestException(`Stock count with ID "${stockCountId}" is not in APPROVED status.`);
        }
        for (const item of stockCount.items) {
            if (item.difference.toNumber() !== 0) {
                const movementType = item.difference.toNumber() > 0 ? prisma_1.StockMovementType.ADJUSTMENT : prisma_1.StockMovementType.ADJUSTMENT;
                const quantity = Math.abs(item.difference.toNumber());
                const inventoryItem = await this.prisma.inventoryItem.findUnique({ where: { id: item.inventoryItemId } });
                if (!inventoryItem) {
                    throw new common_1.NotFoundException(`Inventory item with ID "${item.inventoryItemId}" not found for adjustment.`);
                }
                await this.stockMovementService.createStockMovement({
                    branchId: branchId,
                    inventoryItemId: item.inventoryItemId,
                    type: movementType,
                    quantity: quantity,
                    unit: inventoryItem.unit,
                    unitCost: item.unitCost?.toNumber() || 0,
                    reason: `Stock Count Adjustment (${stockCount.countType} - ${item.difference.toNumber() > 0 ? 'Surplus' : 'Shortage'})`,
                    referenceId: stockCount.id,
                    referenceType: 'STOCK_COUNT',
                    createdBy: userId,
                });
            }
        }
        console.log(`Stock adjustments applied for Stock Count ID: ${stockCountId}`);
    }
    async removeStockCount(id) {
        try {
            const stockCount = await this.prisma.stockCount.delete({
                where: { id },
            });
            return stockCount;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Stock count with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.StockCountService = StockCountService;
exports.StockCountService = StockCountService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        stock_movement_service_1.StockMovementService])
], StockCountService);
//# sourceMappingURL=stock-count.service.js.map