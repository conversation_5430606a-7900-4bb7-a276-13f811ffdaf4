import { PrismaService } from '../prisma/prisma.service';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { ReservationStatus } from '../../generated/prisma';
export declare class ReservationService {
    private prisma;
    constructor(prisma: PrismaService);
    createReservation(data: CreateReservationDto): Promise<{
        branch: {
            name: string;
            address: string;
            phone: string;
            email: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            companyId: string;
            code: string;
            latitude: number | null;
            longitude: number | null;
            serverIp: string | null;
            serverPort: number | null;
            isMainBranch: boolean;
            openingTime: string | null;
            closingTime: string | null;
            workingDays: number[];
            cashRegisterId: string | null;
            posTerminalId: string | null;
            active: boolean;
        };
        customer: {
            taxNumber: string | null;
            taxOffice: string | null;
            address: string | null;
            phone: string;
            email: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            firstName: string | null;
            lastName: string | null;
            birthDate: Date | null;
            version: number;
            companyName: string | null;
            title: string | null;
            phone2: string | null;
            district: string | null;
            city: string | null;
            country: string | null;
            postalCode: string | null;
            gender: string | null;
            marketingConsent: boolean;
            smsConsent: boolean;
            emailConsent: boolean;
            loyaltyPoints: number;
            totalSpent: import("generated/prisma/runtime/library").Decimal;
            orderCount: number;
            lastOrderDate: Date | null;
            currentDebt: import("generated/prisma/runtime/library").Decimal;
            creditLimit: import("generated/prisma/runtime/library").Decimal;
            paymentTerm: number | null;
            segment: string | null;
            tags: string[];
            notes: string | null;
            source: string | null;
            referredBy: string | null;
            blacklisted: boolean;
            blacklistReason: string | null;
            customFields: import("generated/prisma/runtime/library").JsonValue | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import("../../generated/prisma").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("generated/prisma/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("generated/prisma/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
    findAllReservations(branchId?: string, customerId?: string, status?: ReservationStatus, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import("../../generated/prisma").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("generated/prisma/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("generated/prisma/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    })[]>;
    findOneReservation(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import("../../generated/prisma").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("generated/prisma/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("generated/prisma/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
    updateReservation(id: string, data: UpdateReservationDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import("../../generated/prisma").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("generated/prisma/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("generated/prisma/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
    removeReservation(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import("../../generated/prisma").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("generated/prisma/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("generated/prisma/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
}
