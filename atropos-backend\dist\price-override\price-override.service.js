"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceOverrideService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let PriceOverrideService = class PriceOverrideService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createPriceOverride(data) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        const productExists = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
        if (!productExists) {
            throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
        }
        if (data.variantId) {
            const variantExists = await this.prisma.productVariant.findUnique({ where: { id: data.variantId, productId: data.productId, deletedAt: null } });
            if (!variantExists) {
                throw new common_1.NotFoundException(`Product variant with ID "${data.variantId}" not found for product "${data.productId}".`);
            }
        }
        const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByExists) {
            throw new common_1.NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`);
        }
        const newStartDate = new Date(data.startDate);
        const newEndDate = data.endDate ? new Date(data.endDate) : null;
        const overlappingOverrides = await this.prisma.priceOverride.findMany({
            where: {
                branchId: data.branchId,
                productId: data.productId,
                variantId: data.variantId || null,
                OR: [
                    {
                        AND: [
                            { startDate: { lt: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                            { endDate: { gt: newStartDate } },
                        ],
                    },
                    {
                        AND: [
                            { startDate: { lt: newStartDate } },
                            { endDate: { gt: newStartDate } },
                        ],
                    },
                    {
                        AND: [
                            { startDate: { gte: newStartDate } },
                            { endDate: { lte: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                        ],
                    }
                ],
            },
        });
        if (overlappingOverrides.length > 0) {
            throw new common_1.ConflictException('Overlapping price override already exists for this product/variant in the specified time range.');
        }
        return this.prisma.priceOverride.create({
            data: {
                ...data,
                overridePrice: parseFloat(data.overridePrice.toFixed(2)),
                startDate: newStartDate,
                endDate: newEndDate,
            },
        });
    }
    async findAllPriceOverrides(branchId, productId, variantId, activeOnly, date) {
        return this.prisma.priceOverride.findMany({
            where: {
                branchId: branchId || undefined,
                productId: productId || undefined,
                variantId: variantId || undefined,
                AND: [
                    activeOnly !== undefined ? {
                        startDate: { lte: date || new Date() },
                        OR: [
                            { endDate: { gte: date || new Date() } },
                            { endDate: null }
                        ]
                    } : {},
                ]
            },
            include: {
                branch: { select: { id: true, name: true } },
                product: { select: { id: true, name: true, code: true } },
                variant: { select: { id: true, name: true, code: true } },
            },
            orderBy: { startDate: 'desc' },
        });
    }
    async findOnePriceOverride(id) {
        const override = await this.prisma.priceOverride.findUnique({
            where: { id },
            include: {
                branch: { select: { id: true, name: true } },
                product: { select: { id: true, name: true, code: true } },
                variant: { select: { id: true, name: true, code: true } },
            },
        });
        if (!override) {
            throw new common_1.NotFoundException(`Price override with ID "${id}" not found.`);
        }
        return override;
    }
    async updatePriceOverride(id, data) {
        const existingOverride = await this.findOnePriceOverride(id);
        if (data.branchId && data.branchId !== existingOverride.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists) {
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
            }
        }
        const newStartDate = data.startDate ? new Date(data.startDate) : existingOverride.startDate;
        const newEndDate = data.endDate ? new Date(data.endDate) : existingOverride.endDate;
        const targetBranchId = data.branchId || existingOverride.branchId;
        const targetProductId = data.productId || existingOverride.productId;
        const targetVariantId = data.variantId === undefined ? existingOverride.variantId : data.variantId;
        const overlappingOverrides = await this.prisma.priceOverride.findMany({
            where: {
                branchId: targetBranchId,
                productId: targetProductId,
                variantId: targetVariantId,
                id: { not: id },
                OR: [
                    {
                        AND: [
                            { startDate: { lt: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                            { endDate: { gt: newStartDate } },
                        ],
                    },
                    {
                        AND: [
                            { startDate: { lt: newStartDate } },
                            { endDate: { gt: newStartDate } },
                        ],
                    },
                    {
                        AND: [
                            { startDate: { gte: newStartDate } },
                            { endDate: { lte: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                        ],
                    }
                ],
            },
        });
        if (overlappingOverrides.length > 0) {
            throw new common_1.ConflictException('Overlapping price override already exists for this product/variant in the updated time range.');
        }
        try {
            return await this.prisma.priceOverride.update({
                where: { id },
                data: {
                    ...data,
                    overridePrice: data.overridePrice !== undefined ? parseFloat(data.overridePrice.toFixed(2)) : undefined,
                    startDate: data.startDate ? new Date(data.startDate) : undefined,
                    endDate: data.endDate ? new Date(data.endDate) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Price override with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removePriceOverride(id) {
        try {
            return await this.prisma.priceOverride.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Price override with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.PriceOverrideService = PriceOverrideService;
exports.PriceOverrideService = PriceOverrideService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PriceOverrideService);
//# sourceMappingURL=price-override.service.js.map