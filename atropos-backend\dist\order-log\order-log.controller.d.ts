import { OrderLogService } from './order-log.service';
import { CreateOrderLogDto } from './dto/create-order-log.dto';
import { UpdateOrderLogDto } from './dto/update-order-log.dto';
export declare class OrderLogController {
    private readonly orderLogService;
    constructor(orderLogService: OrderLogService);
    create(createOrderLogDto: CreateOrderLogDto): Promise<{
        id: string;
        orderId: string;
        userId: string | null;
        timestamp: Date;
        action: string;
        details: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    findAll(orderId?: string, userId?: string, action?: string, startDate?: Date, endDate?: Date): Promise<({
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        order: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
        };
    } & {
        id: string;
        orderId: string;
        userId: string | null;
        timestamp: Date;
        action: string;
        details: import("generated/prisma/runtime/library").JsonValue | null;
    })[]>;
    findOne(id: string): Promise<{
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        order: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
        };
    } & {
        id: string;
        orderId: string;
        userId: string | null;
        timestamp: Date;
        action: string;
        details: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    update(id: string, updateOrderLogDto: UpdateOrderLogDto): Promise<void>;
    remove(id: string): Promise<void>;
}
