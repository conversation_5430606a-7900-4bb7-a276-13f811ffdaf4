"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrinterGroupService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let PrinterGroupService = class PrinterGroupService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createPrinterGroup(data) {
        const existingGroup = await this.prisma.printerGroup.findUnique({
            where: { name: data.name },
        });
        if (existingGroup) {
            throw new common_1.ConflictException(`Printer group with name "${data.name}" already exists.`);
        }
        if (data.categoryIds && data.categoryIds.length > 0) {
            const categories = await this.prisma.category.findMany({
                where: { id: { in: data.categoryIds }, deletedAt: null },
            });
            if (categories.length !== data.categoryIds.length) {
                const foundIds = categories.map(c => c.id);
                const notFoundIds = data.categoryIds.filter(id => !foundIds.includes(id));
                throw new common_1.NotFoundException(`Some categories not found: ${notFoundIds.join(', ')}.`);
            }
        }
        return this.prisma.printerGroup.create({
            data: {
                name: data.name,
                categories: {
                    connect: data.categoryIds?.map(id => ({ id })) || []
                }
            },
            include: { categories: { select: { id: true, name: true } } }
        });
    }
    async findAllPrinterGroups() {
        return this.prisma.printerGroup.findMany({
            include: { categories: { select: { id: true, name: true } }, printers: true },
            orderBy: { name: 'asc' },
        });
    }
    async findOnePrinterGroup(id) {
        const group = await this.prisma.printerGroup.findUnique({
            where: { id },
            include: { categories: { select: { id: true, name: true } }, printers: true },
        });
        if (!group) {
            throw new common_1.NotFoundException(`Printer group with ID "${id}" not found.`);
        }
        return group;
    }
    async updatePrinterGroup(id, data) {
        const existingGroup = await this.findOnePrinterGroup(id);
        if (data.name && data.name !== existingGroup.name) {
            const existingGroupByName = await this.prisma.printerGroup.findUnique({
                where: { name: data.name },
            });
            if (existingGroupByName && existingGroupByName.id !== id) {
                throw new common_1.ConflictException(`Printer group with name "${data.name}" already exists.`);
            }
        }
        if (data.categoryIds !== undefined) {
            const categories = await this.prisma.category.findMany({
                where: { id: { in: data.categoryIds }, deletedAt: null },
            });
            if (categories.length !== data.categoryIds.length) {
                const foundIds = categories.map(c => c.id);
                const notFoundIds = data.categoryIds.filter(id => !foundIds.includes(id));
                throw new common_1.NotFoundException(`Some categories not found: ${notFoundIds.join(', ')}.`);
            }
            await this.prisma.printerGroup.update({
                where: { id },
                data: {
                    categories: {
                        set: data.categoryIds.map(categoryId => ({ id: categoryId }))
                    }
                }
            });
        }
        try {
            return await this.prisma.printerGroup.update({
                where: { id },
                data: {
                    name: data.name,
                },
                include: { categories: { select: { id: true, name: true } } }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Printer group with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removePrinterGroup(id) {
        const printersCount = await this.prisma.printer.count({
            where: { printerGroupId: id }
        });
        if (printersCount > 0) {
            throw new common_1.ConflictException(`Printer group with ID "${id}" cannot be deleted because it has ${printersCount} associated printers.`);
        }
        const categoriesCount = await this.prisma.category.count({
            where: { printerGroupId: id, deletedAt: null }
        });
        if (categoriesCount > 0) {
            throw new common_1.ConflictException(`Printer group with ID "${id}" cannot be deleted because it is assigned to ${categoriesCount} active categories.`);
        }
        try {
            return await this.prisma.printerGroup.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Printer group with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.PrinterGroupService = PrinterGroupService;
exports.PrinterGroupService = PrinterGroupService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PrinterGroupService);
//# sourceMappingURL=printer-group.service.js.map