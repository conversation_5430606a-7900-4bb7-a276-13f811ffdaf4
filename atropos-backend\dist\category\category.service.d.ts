import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
export declare class CategoryService {
    private prisma;
    constructor(prisma: PrismaService);
    createCategory(data: CreateCategoryDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
    findAllCategories(companyId?: string, parentId?: string): Promise<({
        parent: {
            name: string;
            id: string;
        } | null;
        children: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    })[]>;
    findOneCategory(id: string): Promise<{
        parent: {
            name: string;
            id: string;
        } | null;
        children: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
    updateCategory(id: string, data: UpdateCategoryDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
    removeCategory(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        version: number;
        parentId: string | null;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        printerGroupId: string | null;
    }>;
}
