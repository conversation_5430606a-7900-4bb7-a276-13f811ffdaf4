"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceOverrideController = void 0;
const common_1 = require("@nestjs/common");
const price_override_service_1 = require("./price-override.service");
const create_price_override_dto_1 = require("./dto/create-price-override.dto");
const update_price_override_dto_1 = require("./dto/update-price-override.dto");
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
let PriceOverrideController = class PriceOverrideController {
    priceOverrideService;
    constructor(priceOverrideService) {
        this.priceOverrideService = priceOverrideService;
    }
    create(createPriceOverrideDto) {
        return this.priceOverrideService.createPriceOverride(createPriceOverrideDto);
    }
    findAll(branchId, productId, variantId, activeOnly, date) {
        return this.priceOverrideService.findAllPriceOverrides(branchId, productId, variantId, activeOnly, date);
    }
    findOne(id) {
        return this.priceOverrideService.findOnePriceOverride(id);
    }
    update(id, updatePriceOverrideDto) {
        return this.priceOverrideService.updatePriceOverride(id, updatePriceOverrideDto);
    }
    remove(id) {
        return this.priceOverrideService.removePriceOverride(id);
    }
};
exports.PriceOverrideController = PriceOverrideController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_price_override_dto_1.CreatePriceOverrideDto]),
    __metadata("design:returntype", void 0)
], PriceOverrideController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('branchId')),
    __param(1, (0, common_1.Query)('productId')),
    __param(2, (0, common_1.Query)('variantId')),
    __param(3, (0, common_1.Query)('activeOnly', new common_1.ParseBoolPipe({ optional: true }))),
    __param(4, (0, common_1.Query)('date', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Boolean, Date]),
    __metadata("design:returntype", void 0)
], PriceOverrideController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PriceOverrideController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_price_override_dto_1.UpdatePriceOverrideDto]),
    __metadata("design:returntype", void 0)
], PriceOverrideController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PriceOverrideController.prototype, "remove", null);
exports.PriceOverrideController = PriceOverrideController = __decorate([
    (0, common_1.Controller)('price-override'),
    __metadata("design:paramtypes", [price_override_service_1.PriceOverrideService])
], PriceOverrideController);
//# sourceMappingURL=price-override.controller.js.map