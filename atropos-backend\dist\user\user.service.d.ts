import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from '../../generated/prisma';
export declare class UserService {
    private prisma;
    constructor(prisma: PrismaService);
    findByUsername(username: string): Promise<User | null>;
    createUser(data: CreateUserDto): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        password: string;
        pin: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("../../generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue | null;
        refreshToken: string | null;
        lockedUntil: Date | null;
        version: number;
    }>;
    findAllUsers(companyId?: string, branchId?: string): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("../../generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue;
        lockedUntil: Date | null;
        version: number;
    }[]>;
    findOneUser(id: string): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("../../generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue;
        lockedUntil: Date | null;
        version: number;
    }>;
    updateUser(id: string, data: UpdateUserDto): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("../../generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue;
        lockedUntil: Date | null;
        version: number;
    }>;
    removeUser(id: string): Promise<{
        id: string;
        deletedAt: Date | null;
        active: boolean;
    }>;
}
