"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let PaymentService = class PaymentService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createPayment(data) {
        const order = await this.prisma.order.findUnique({
            where: { id: data.orderId, deletedAt: null },
            include: { branch: { select: { companyId: true } } },
        });
        if (!order) {
            throw new common_1.NotFoundException(`Order with ID "${data.orderId}" not found.`);
        }
        const paymentMethod = await this.prisma.paymentMethod.findUnique({
            where: { id: data.paymentMethodId, deletedAt: null },
        });
        if (!paymentMethod) {
            throw new common_1.NotFoundException(`Payment method with ID "${data.paymentMethodId}" not found.`);
        }
        const totalPaidForOrder = await this.prisma.payment.aggregate({
            where: { orderId: data.orderId, status: { in: ['PAID', 'PARTIALLY_PAID'] }, deletedAt: null },
            _sum: { amount: true },
        });
        const currentlyPaid = totalPaidForOrder._sum.amount?.toNumber() || 0;
        const remainingAmount = order.totalAmount.toNumber() - currentlyPaid;
        if (data.amount > remainingAmount + 0.01) {
            throw new common_1.BadRequestException(`Payment amount (${data.amount}) exceeds remaining order total (${remainingAmount.toFixed(2)}).`);
        }
        const paymentStatus = data.amount >= remainingAmount ? 'PAID' : 'PARTIALLY_PAID';
        let cashMovementId;
        if (paymentMethod.type === 'CASH') {
            const cashMovement = await this.prisma.cashMovement.create({
                data: {
                    branchId: order.branchId,
                    userId: order.cashierId || order.waiterId || (await this.prisma.user.findFirst({ where: { companyId: order.branch.companyId, role: 'ADMIN' } }))?.id || 'system-user-id',
                    type: prisma_1.CashMovementType.SALE,
                    amount: parseFloat(data.amount.toFixed(2)),
                    description: `Order ${order.orderNumber} Payment`,
                    referenceId: order.id,
                    referenceType: 'ORDER',
                    paymentMethodId: paymentMethod.id,
                    previousBalance: 0,
                    currentBalance: 0,
                },
            });
            cashMovementId = cashMovement.id;
        }
        const payment = await this.prisma.payment.create({
            data: {
                ...data,
                status: paymentStatus,
                amount: parseFloat(data.amount.toFixed(2)),
                tipAmount: data.tipAmount !== undefined ? parseFloat(data.tipAmount.toFixed(2)) : undefined,
                changeAmount: data.changeAmount !== undefined ? parseFloat(data.changeAmount.toFixed(2)) : undefined,
                installments: data.installments,
                cashMovementId: cashMovementId,
            },
            include: { order: true, paymentMethod: true },
        });
        await this.prisma.order.update({
            where: { id: order.id },
            data: {
                paidAmount: parseFloat((currentlyPaid + data.amount).toFixed(2)),
                paymentStatus: paymentStatus,
            },
        });
        return payment;
    }
    async findAllPayments(orderId, paymentMethodId, status) {
        return this.prisma.payment.findMany({
            where: {
                orderId: orderId || undefined,
                paymentMethodId: paymentMethodId || undefined,
                status: status || undefined,
                deletedAt: null,
            },
            include: {
                order: { select: { id: true, orderNumber: true, totalAmount: true, paymentStatus: true } },
                paymentMethod: { select: { id: true, name: true, code: true } },
            },
            orderBy: { paidAt: 'desc' },
        });
    }
    async findOnePayment(id) {
        const payment = await this.prisma.payment.findUnique({
            where: { id, deletedAt: null },
            include: {
                order: { select: { id: true, orderNumber: true, totalAmount: true, paymentStatus: true } },
                paymentMethod: { select: { id: true, name: true, code: true } },
                cashMovement: true,
            },
        });
        if (!payment) {
            throw new common_1.NotFoundException(`Payment with ID "${id}" not found.`);
        }
        return payment;
    }
    async refundPayment(id, refundDto) {
        const payment = await this.prisma.payment.findUnique({
            where: { id, deletedAt: null },
            include: { order: { include: { branch: { select: { companyId: true } } } }, paymentMethod: true },
        });
        if (!payment) {
            throw new common_1.NotFoundException(`Payment with ID "${id}" not found.`);
        }
        if (payment.status === 'REFUNDED' || payment.status === 'PARTIALLY_REFUNDED') {
            throw new common_1.BadRequestException('Payment has already been refunded.');
        }
        if (refundDto.refundAmount > payment.amount.toNumber()) {
            throw new common_1.BadRequestException('Refund amount cannot exceed original payment amount.');
        }
        const newStatus = refundDto.refundAmount === payment.amount.toNumber() ? 'REFUNDED' : 'PARTIALLY_REFUNDED';
        let cashMovementId;
        if (payment.paymentMethod.type === 'CASH') {
            const cashMovement = await this.prisma.cashMovement.create({
                data: {
                    branchId: payment.order.branchId,
                    userId: refundDto.refundedBy || (await this.prisma.user.findFirst({ where: { companyId: payment.order.branch.companyId, role: 'ADMIN' } }))?.id || 'system-user-id',
                    type: prisma_1.CashMovementType.REFUND,
                    amount: parseFloat(refundDto.refundAmount.toFixed(2)) * -1,
                    description: `Refund for Order ${payment.order.orderNumber} Payment ${payment.id}`,
                    referenceId: payment.order.id,
                    referenceType: 'ORDER',
                    paymentMethodId: payment.paymentMethodId,
                    previousBalance: 0,
                    currentBalance: 0,
                },
            });
            cashMovementId = cashMovement.id;
        }
        const refundedPayment = await this.prisma.payment.update({
            where: { id },
            data: {
                status: newStatus,
                refundAmount: parseFloat(((payment.refundAmount?.toNumber() || 0) + refundDto.refundAmount).toFixed(2)),
                refundReason: refundDto.refundReason,
                refundedAt: new Date(),
                cashMovementId: cashMovementId,
            },
            include: { order: true, paymentMethod: true },
        });
        const totalPaidForOrderAfterRefund = await this.prisma.payment.aggregate({
            where: { orderId: payment.orderId, status: { in: ['PAID', 'PARTIALLY_PAID'] }, deletedAt: null },
            _sum: { amount: true },
        });
        const currentlyPaidAfterRefund = totalPaidForOrderAfterRefund._sum.amount?.toNumber() || 0;
        let orderPaymentStatus;
        if (currentlyPaidAfterRefund <= 0) {
            orderPaymentStatus = 'UNPAID';
        }
        else if (currentlyPaidAfterRefund >= payment.order.totalAmount.toNumber() - 0.01) {
            orderPaymentStatus = 'PAID';
        }
        else {
            orderPaymentStatus = 'PARTIALLY_PAID';
        }
        await this.prisma.order.update({
            where: { id: payment.orderId },
            data: {
                paidAmount: parseFloat(currentlyPaidAfterRefund.toFixed(2)),
                paymentStatus: orderPaymentStatus,
            },
        });
        return refundedPayment;
    }
    async removePayment(id) {
        try {
            const payment = await this.prisma.payment.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), status: 'VOIDED' },
            });
            const totalPaidForOrder = await this.prisma.payment.aggregate({
                where: { orderId: payment.orderId, status: { in: ['PAID', 'PARTIALLY_PAID'] }, deletedAt: null },
                _sum: { amount: true },
            });
            const currentlyPaid = totalPaidForOrder._sum.amount?.toNumber() || 0;
            const order = await this.prisma.order.findUnique({ where: { id: payment.orderId } });
            let orderPaymentStatus;
            if (currentlyPaid <= 0) {
                orderPaymentStatus = 'UNPAID';
            }
            else if (order && currentlyPaid >= order.totalAmount.toNumber() - 0.01) {
                orderPaymentStatus = 'PAID';
            }
            else {
                orderPaymentStatus = 'PARTIALLY_PAID';
            }
            if (order) {
                await this.prisma.order.update({
                    where: { id: order.id },
                    data: {
                        paidAmount: parseFloat(currentlyPaid.toFixed(2)),
                        paymentStatus: orderPaymentStatus,
                    },
                });
            }
            return payment;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Payment with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PaymentService);
//# sourceMappingURL=payment.service.js.map