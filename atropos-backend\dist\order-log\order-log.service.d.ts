import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderLogDto } from './dto/create-order-log.dto';
import { UpdateOrderLogDto } from './dto/update-order-log.dto';
export declare class OrderLogService {
    private prisma;
    constructor(prisma: PrismaService);
    createOrderLog(data: CreateOrderLogDto): Promise<{
        id: string;
        orderId: string;
        userId: string | null;
        timestamp: Date;
        action: string;
        details: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    findAllOrderLogs(orderId?: string, userId?: string, action?: string, startDate?: Date, endDate?: Date): Promise<({
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        order: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
        };
    } & {
        id: string;
        orderId: string;
        userId: string | null;
        timestamp: Date;
        action: string;
        details: import("generated/prisma/runtime/library").JsonValue | null;
    })[]>;
    findOneOrderLog(id: string): Promise<{
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        order: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
        };
    } & {
        id: string;
        orderId: string;
        userId: string | null;
        timestamp: Date;
        action: string;
        details: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    updateOrderLog(id: string, data: UpdateOrderLogDto): Promise<void>;
    removeOrderLog(id: string): Promise<void>;
}
