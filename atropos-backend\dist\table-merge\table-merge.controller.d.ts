import { TableMergeService } from './table-merge.service';
import { CreateTableMergeDto } from './dto/create-table-merge.dto';
import { UpdateTableMergeDto } from './dto/update-table-merge.dto';
export declare class TableMergeController {
    private readonly tableMergeService;
    constructor(tableMergeService: TableMergeService);
    create(createTableMergeDto: CreateTableMergeDto): Promise<{
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    }>;
    findAll(tableId?: string, targetId?: string): Promise<({
        mainTable: {
            number: string;
            name: string | null;
            id: string;
            branchId: string;
            status: import("generated/prisma").$Enums.TableStatus;
        };
        mergedTable: {
            number: string;
            name: string | null;
            id: string;
            branchId: string;
            status: import("generated/prisma").$Enums.TableStatus;
        };
    } & {
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    })[]>;
    findOne(id: string): Promise<{
        mainTable: {
            number: string;
            name: string | null;
            id: string;
            branchId: string;
            status: import("generated/prisma").$Enums.TableStatus;
        };
        mergedTable: {
            number: string;
            name: string | null;
            id: string;
            branchId: string;
            status: import("generated/prisma").$Enums.TableStatus;
        };
    } & {
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    }>;
    update(id: string, updateTableMergeDto: UpdateTableMergeDto): Promise<void>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    }>;
}
