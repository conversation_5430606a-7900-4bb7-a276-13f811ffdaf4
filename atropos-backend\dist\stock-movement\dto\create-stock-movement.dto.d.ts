import { ProductUnit, StockMovementType } from '../../../generated/prisma';
export declare class CreateStockMovementDto {
    branchId: string;
    productId?: string;
    inventoryItemId?: string;
    type: StockMovementType;
    reason?: string;
    quantity: number;
    unit: ProductUnit;
    unitCost?: number;
    referenceId?: string;
    referenceType?: string;
    referenceNo?: string;
    fromBranchId?: string;
    toBranchId?: string;
    supplierId?: string;
    invoiceNo?: string;
    note?: string;
    attachments?: string[];
    createdBy: string;
    approvedBy?: string;
}
