"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableAreaController = void 0;
const common_1 = require("@nestjs/common");
const table_area_service_1 = require("./table-area.service");
const create_table_area_dto_1 = require("./dto/create-table-area.dto");
const update_table_area_dto_1 = require("./dto/update-table-area.dto");
let TableAreaController = class TableAreaController {
    tableAreaService;
    constructor(tableAreaService) {
        this.tableAreaService = tableAreaService;
    }
    create(createTableAreaDto) {
        return this.tableAreaService.createTableArea(createTableAreaDto);
    }
    findAll(branchId) {
        return this.tableAreaService.findAllTableAreas(branchId);
    }
    findOne(id) {
        return this.tableAreaService.findOneTableArea(id);
    }
    update(id, updateTableAreaDto) {
        return this.tableAreaService.updateTableArea(id, updateTableAreaDto);
    }
    remove(id) {
        return this.tableAreaService.removeTableArea(id);
    }
};
exports.TableAreaController = TableAreaController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_table_area_dto_1.CreateTableAreaDto]),
    __metadata("design:returntype", void 0)
], TableAreaController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('branchId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TableAreaController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TableAreaController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_table_area_dto_1.UpdateTableAreaDto]),
    __metadata("design:returntype", void 0)
], TableAreaController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TableAreaController.prototype, "remove", null);
exports.TableAreaController = TableAreaController = __decorate([
    (0, common_1.Controller)('table-area'),
    __metadata("design:paramtypes", [table_area_service_1.TableAreaService])
], TableAreaController);
//# sourceMappingURL=table-area.controller.js.map