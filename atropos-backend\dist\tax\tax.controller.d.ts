import { TaxService } from './tax.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
export declare class TaxController {
    private readonly taxService;
    constructor(taxService: TaxService);
    create(createTaxDto: CreateTaxDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
    findAll(companyId?: string): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
    update(id: string, updateTaxDto: UpdateTaxDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        rate: import("generated/prisma/runtime/library").Decimal;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
    }>;
}
