"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DailyReportController = void 0;
const common_1 = require("@nestjs/common");
const daily_report_service_1 = require("./daily-report.service");
const create_daily_report_dto_1 = require("./dto/create-daily-report.dto");
const update_daily_report_dto_1 = require("./dto/update-daily-report.dto");
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
let DailyReportController = class DailyReportController {
    dailyReportService;
    constructor(dailyReportService) {
        this.dailyReportService = dailyReportService;
    }
    async create(createDailyReportDto) {
        return this.dailyReportService.createDailyReport(createDailyReportDto);
    }
    findAll(branchId, startDate, endDate) {
        return this.dailyReportService.findAllDailyReports(branchId, startDate, endDate);
    }
    findOne(id) {
        return this.dailyReportService.findOneDailyReport(id);
    }
    update(id, updateDailyReportDto) {
        return this.dailyReportService.updateDailyReport(id, updateDailyReportDto);
    }
    remove(id) {
        return this.dailyReportService.removeDailyReport(id);
    }
};
exports.DailyReportController = DailyReportController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_daily_report_dto_1.CreateDailyReportDto]),
    __metadata("design:returntype", Promise)
], DailyReportController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('branchId')),
    __param(1, (0, common_1.Query)('startDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __param(2, (0, common_1.Query)('endDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Date,
        Date]),
    __metadata("design:returntype", void 0)
], DailyReportController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DailyReportController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_daily_report_dto_1.UpdateDailyReportDto]),
    __metadata("design:returntype", void 0)
], DailyReportController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DailyReportController.prototype, "remove", null);
exports.DailyReportController = DailyReportController = __decorate([
    (0, common_1.Controller)('daily-report'),
    __metadata("design:paramtypes", [daily_report_service_1.DailyReportService])
], DailyReportController);
//# sourceMappingURL=daily-report.controller.js.map