declare enum OnlineOrderStatus {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED",
    PREPARING = "PREPARING",
    READY = "READY",
    DELIVERING = "DELIVERING",
    DELIVERED = "DELIVERED",
    CANCELLED = "CANCELLED",
    RETURNED = "RETURNED"
}
declare class OnlineOrderItemDto {
    platformProductId: string;
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice?: number;
    note?: string;
}
export declare class CreateOnlineOrderDto {
    platformId: string;
    orderId?: string;
    platformOrderId: string;
    platformOrderNo: string;
    customerName: string;
    phone: string;
    customerEmail?: string;
    deliveryAddress: string;
    deliveryNote?: string;
    orderData?: any;
    status?: OnlineOrderStatus;
    platformStatus?: string;
    subtotal: number;
    deliveryFee?: number;
    serviceFee?: number;
    discount?: number;
    totalAmount: number;
    commissionAmount?: number;
    netAmount?: number;
    paymentMethod: string;
    isPaid?: boolean;
    orderedAt: Date;
    requestedAt?: Date;
    rejectReason?: string;
    cancelReason?: string;
    items: OnlineOrderItemDto[];
}
export {};
