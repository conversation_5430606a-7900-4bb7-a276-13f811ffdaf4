"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationTemplateModule = void 0;
const common_1 = require("@nestjs/common");
const notification_template_service_1 = require("./notification-template.service");
const notification_template_controller_1 = require("./notification-template.controller");
const prisma_module_1 = require("../prisma/prisma.module");
let NotificationTemplateModule = class NotificationTemplateModule {
};
exports.NotificationTemplateModule = NotificationTemplateModule;
exports.NotificationTemplateModule = NotificationTemplateModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [notification_template_controller_1.NotificationTemplateController],
        providers: [notification_template_service_1.NotificationTemplateService],
        exports: [notification_template_service_1.NotificationTemplateService],
    })
], NotificationTemplateModule);
//# sourceMappingURL=notification-template.module.js.map