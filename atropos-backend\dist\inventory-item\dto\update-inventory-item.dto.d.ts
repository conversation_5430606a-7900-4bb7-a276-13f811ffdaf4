declare enum ProductUnit {
    PIECE = "PIECE",
    KG = "KG",
    GRAM = "GRAM",
    LITER = "LITER",
    ML = "ML"
}
export declare class UpdateInventoryItemDto {
    productId?: string;
    name?: string;
    code?: string;
    barcode?: string;
    unit?: ProductUnit;
    currentStock?: number;
    reservedStock?: number;
    availableStock?: number;
    criticalLevel?: number;
    optimalLevel?: number;
    lastCost?: number;
    averageCost?: number;
    supplier?: string;
    supplierCode?: string;
    location?: string;
    expiryDate?: Date;
    active?: boolean;
}
export {};
