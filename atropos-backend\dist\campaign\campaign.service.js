"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CampaignService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let CampaignService = class CampaignService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createCampaign(data) {
        const companyExists = await this.prisma.company.findUnique({
            where: { id: data.companyId, deletedAt: null },
        });
        if (!companyExists) {
            throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
        const existingCampaign = await this.prisma.campaign.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingCampaign) {
            throw new common_1.ConflictException(`Campaign with code "${data.code}" already exists for this company.`);
        }
        if (data.campaignType === prisma_1.CampaignType.DISCOUNT) {
            if (!data.discountType || data.discountValue === undefined) {
                throw new common_1.BadRequestException('discountType and discountValue are required for DISCOUNT campaigns.');
            }
        }
        else {
            if (data.discountType !== undefined || data.discountValue !== undefined) {
                throw new common_1.BadRequestException('discountType and discountValue should not be provided for non-DISCOUNT campaigns.');
            }
        }
        return this.prisma.campaign.create({
            data: {
                ...data,
                discountValue: data.discountValue !== undefined ? parseFloat(data.discountValue.toFixed(2)) : undefined,
                minOrderAmount: data.minOrderAmount !== undefined ? parseFloat(data.minOrderAmount.toFixed(2)) : undefined,
                maxDiscountAmount: data.maxDiscountAmount !== undefined ? parseFloat(data.maxDiscountAmount.toFixed(2)) : undefined,
                startDate: new Date(data.startDate),
                endDate: data.endDate ? new Date(data.endDate) : undefined,
            },
        });
    }
    async findAllCampaigns(companyId, campaignType, active) {
        return this.prisma.campaign.findMany({
            where: {
                companyId: companyId || undefined,
                campaignType: campaignType || undefined,
                active: active !== undefined ? active : undefined,
            },
            include: { company: { select: { id: true, name: true } } },
            orderBy: { startDate: 'desc' },
        });
    }
    async findOneCampaign(id) {
        const campaign = await this.prisma.campaign.findUnique({
            where: { id },
            include: { company: { select: { id: true, name: true } } },
        });
        if (!campaign) {
            throw new common_1.NotFoundException(`Campaign with ID "${id}" not found.`);
        }
        return campaign;
    }
    async updateCampaign(id, data) {
        const existingCampaign = await this.findOneCampaign(id);
        if (data.companyId && data.companyId !== existingCampaign.companyId) {
            const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
            if (!companyExists) {
                throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
            }
        }
        if (data.code && data.code !== existingCampaign.code) {
            const existingCampaignByCode = await this.prisma.campaign.findUnique({
                where: {
                    companyId_code: {
                        companyId: data.companyId || existingCampaign.companyId,
                        code: data.code,
                    },
                },
            });
            if (existingCampaignByCode && existingCampaignByCode.id !== id) {
                throw new common_1.ConflictException(`Campaign with code "${data.code}" already exists for this company.`);
            }
        }
        const targetCampaignType = data.campaignType || existingCampaign.campaignType;
        if (targetCampaignType === prisma_1.CampaignType.DISCOUNT) {
            if (data.discountType === undefined && existingCampaign.discountType === undefined && data.discountValue === undefined && existingCampaign.discountValue === undefined) {
                if (data.campaignType === prisma_1.CampaignType.DISCOUNT && existingCampaign.campaignType !== prisma_1.CampaignType.DISCOUNT) {
                    throw new common_1.BadRequestException('discountType and discountValue are required when changing to DISCOUNT campaign type.');
                }
            }
        }
        else {
            if (data.discountType !== undefined || data.discountValue !== undefined) {
                throw new common_1.BadRequestException('discountType and discountValue should not be provided for non-DISCOUNT campaigns.');
            }
        }
        try {
            return await this.prisma.campaign.update({
                where: { id },
                data: {
                    ...data,
                    discountValue: data.discountValue !== undefined ? parseFloat(data.discountValue.toFixed(2)) : undefined,
                    minOrderAmount: data.minOrderAmount !== undefined ? parseFloat(data.minOrderAmount.toFixed(2)) : undefined,
                    maxDiscountAmount: data.maxDiscountAmount !== undefined ? parseFloat(data.maxDiscountAmount.toFixed(2)) : undefined,
                    startDate: data.startDate ? new Date(data.startDate) : undefined,
                    endDate: data.endDate ? new Date(data.endDate) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Campaign with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeCampaign(id) {
        const usagesCount = await this.prisma.campaignUsage.count({
            where: { campaignId: id }
        });
        if (usagesCount > 0) {
            throw new common_1.ConflictException(`Campaign with ID "${id}" cannot be deleted because it has ${usagesCount} associated usages.`);
        }
        try {
            return await this.prisma.campaign.update({
                where: { id },
                data: { active: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Campaign with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.CampaignService = CampaignService;
exports.CampaignService = CampaignService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CampaignService);
//# sourceMappingURL=campaign.service.js.map