"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderLogService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let OrderLogService = class OrderLogService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createOrderLog(data) {
        const orderExists = await this.prisma.order.findUnique({
            where: { id: data.orderId, deletedAt: null },
        });
        if (!orderExists) {
            throw new common_1.NotFoundException(`Order with ID "${data.orderId}" not found for order log.`);
        }
        if (data.userId) {
            const userExists = await this.prisma.user.findUnique({
                where: { id: data.userId, deletedAt: null },
            });
            if (!userExists) {
                throw new common_1.NotFoundException(`User with ID "${data.userId}" not found for order log.`);
            }
        }
        return this.prisma.orderLog.create({
            data: {
                ...data,
                timestamp: new Date(),
            },
        });
    }
    async findAllOrderLogs(orderId, userId, action, startDate, endDate) {
        return this.prisma.orderLog.findMany({
            where: {
                orderId: orderId || undefined,
                userId: userId || undefined,
                action: action || undefined,
                timestamp: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
            },
            include: {
                order: { select: { id: true, orderNumber: true, status: true } },
                user: { select: { id: true, username: true, firstName: true, lastName: true } },
            },
            orderBy: { timestamp: 'desc' },
        });
    }
    async findOneOrderLog(id) {
        const log = await this.prisma.orderLog.findUnique({
            where: { id },
            include: {
                order: { select: { id: true, orderNumber: true, status: true } },
                user: { select: { id: true, username: true, firstName: true, lastName: true } },
            },
        });
        if (!log) {
            throw new common_1.NotFoundException(`Order log with ID "${id}" not found.`);
        }
        return log;
    }
    async updateOrderLog(id, data) {
        throw new common_1.ForbiddenException('Order logs cannot be updated.');
    }
    async removeOrderLog(id) {
        throw new common_1.ForbiddenException('Order logs cannot be deleted.');
    }
};
exports.OrderLogService = OrderLogService;
exports.OrderLogService = OrderLogService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrderLogService);
//# sourceMappingURL=order-log.service.js.map