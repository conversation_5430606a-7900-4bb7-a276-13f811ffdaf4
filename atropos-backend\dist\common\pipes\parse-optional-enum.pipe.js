"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParseOptionalEnumPipe = void 0;
const common_1 = require("@nestjs/common");
let ParseOptionalEnumPipe = class ParseOptionalEnumPipe {
    enumObject;
    constructor(enumObject) {
        this.enumObject = enumObject;
    }
    transform(value, metadata) {
        if (!value) {
            return undefined;
        }
        const enumValues = Object.values(this.enumObject);
        if (!enumValues.includes(value)) {
            throw new common_1.BadRequestException(`Validation failed (${enumValues.join(', ')} expected for ${metadata.data}).`);
        }
        return value;
    }
};
exports.ParseOptionalEnumPipe = ParseOptionalEnumPipe;
exports.ParseOptionalEnumPipe = ParseOptionalEnumPipe = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object])
], ParseOptionalEnumPipe);
//# sourceMappingURL=parse-optional-enum.pipe.js.map