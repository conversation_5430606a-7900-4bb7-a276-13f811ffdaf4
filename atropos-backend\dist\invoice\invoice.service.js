"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const InvoiceType = {
    RECEIPT: 'RECEIPT',
    INVOICE: 'INVOICE',
    E_ARCHIVE: 'E_ARCHIVE',
    E_INVOICE: 'E_INVOICE',
    PROFORMA: 'PROFORMA',
    RETURN: 'RETURN'
};
const EArchiveStatus = {
    PENDING: 'PENDING',
    SENT: 'SENT',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED',
    CANCELLED: 'CANCELLED'
};
let InvoiceService = class InvoiceService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async generateInvoiceNumber(invoiceType, serialNo) {
        const currentYearPrefix = new Date().getFullYear().toString();
        const latestInvoice = await this.prisma.invoice.findFirst({
            where: { serialNo, sequenceNo: { startsWith: currentYearPrefix } },
            orderBy: { createdAt: 'desc' }
        });
        let newSequence = 1;
        if (latestInvoice && latestInvoice.sequenceNo) {
            const lastSeqNum = parseInt(latestInvoice.sequenceNo.substring(currentYearPrefix.length));
            newSequence = lastSeqNum + 1;
        }
        return `${currentYearPrefix}${String(newSequence).padStart(6, '0')}`;
    }
    async createInvoice(data) {
        const existingInvoiceByNumber = await this.prisma.invoice.findUnique({
            where: {
                serialNo_sequenceNo: {
                    serialNo: data.serialNo,
                    sequenceNo: data.sequenceNo || '',
                },
            },
        });
        if (existingInvoiceByNumber) {
            throw new common_1.ConflictException(`Invoice with serial number "${data.serialNo}" and sequence number "${data.sequenceNo}" already exists.`);
        }
        let order = null;
        if (data.orderId) {
            order = await this.prisma.order.findUnique({
                where: { id: data.orderId, deletedAt: null },
                include: {
                    items: { include: { product: { include: { tax: true } } } },
                    customer: true
                },
            });
            if (!order) {
                throw new common_1.NotFoundException(`Order with ID "${data.orderId}" not found.`);
            }
            const existingInvoiceForOrder = await this.prisma.invoice.findUnique({
                where: { orderId: data.orderId },
            });
            if (existingInvoiceForOrder) {
                throw new common_1.ConflictException(`An invoice already exists for order with ID "${data.orderId}".`);
            }
        }
        let subtotal;
        let discountAmount;
        let taxAmount;
        let totalAmount;
        let taxDetails;
        if (order) {
            subtotal = order.subtotal.toNumber();
            discountAmount = order.discountAmount.toNumber();
            taxAmount = order.taxAmount.toNumber();
            totalAmount = order.totalAmount.toNumber();
            const calculatedTaxDetails = {};
            for (const item of order.items) {
                const itemSubtotal = (item.unitPrice.toNumber() * item.quantity.toNumber()) - (item.discountAmount.toNumber() || (item.discountRate.toNumber() ? (item.unitPrice.toNumber() * item.discountRate.toNumber()) / 100 : 0));
                const taxRate = item.taxRate.toNumber();
                const taxCode = item.product.tax.code;
                if (!calculatedTaxDetails[taxCode]) {
                    calculatedTaxDetails[taxCode] = { name: item.product.tax.name, rate: taxRate, base: 0, amount: 0 };
                }
                calculatedTaxDetails[taxCode].base += itemSubtotal;
                calculatedTaxDetails[taxCode].amount += (itemSubtotal * taxRate) / 100;
            }
            taxDetails = calculatedTaxDetails;
            data.customerName = data.customerName || order.customerName || (order.customer ? order.customer.firstName + ' ' + order.customer.lastName : undefined);
            data.taxNumber = data.taxNumber || order.customer?.taxNumber;
            data.taxOffice = data.taxOffice || order.customer?.taxOffice;
            data.address = data.address || order.customer?.address || order.deliveryAddress;
            data.phone = data.phone || order.customerPhone || order.customer?.phone;
            data.email = data.email || order.customer?.email;
        }
        else {
            if (data.subtotal === undefined || data.taxAmount === undefined || data.totalAmount === undefined) {
                throw new common_1.BadRequestException('For manual invoices, subtotal, taxAmount, and totalAmount must be provided.');
            }
            subtotal = data.subtotal;
            discountAmount = data.discountAmount || 0;
            taxAmount = data.taxAmount;
            totalAmount = data.totalAmount;
            taxDetails = data.taxDetails || {};
        }
        if (!data.sequenceNo) {
            data.sequenceNo = await this.generateInvoiceNumber(data.invoiceType, data.serialNo);
        }
        const invoice = await this.prisma.invoice.create({
            data: {
                orderId: order?.id,
                invoiceType: data.invoiceType,
                serialNo: data.serialNo,
                sequenceNo: data.sequenceNo,
                customerName: data.customerName,
                customerTaxNo: data.taxNumber,
                customerTaxOffice: data.taxOffice,
                customerAddress: data.address,
                customerPhone: data.phone,
                customerEmail: data.email,
                subtotal: parseFloat(subtotal.toFixed(2)),
                discountAmount: parseFloat(discountAmount.toFixed(2)),
                taxAmount: parseFloat(taxAmount.toFixed(2)),
                totalAmount: parseFloat(totalAmount.toFixed(2)),
                totalAmountText: data.totalAmountText,
                taxDetails: taxDetails,
                eArchiveStatus: data.eArchiveStatus || (data.invoiceType === 'E_ARCHIVE' ? 'PENDING' : undefined),
            },
        });
        if (order) {
            await this.prisma.order.update({
                where: { id: order.id },
                data: { invoice: { connect: { id: invoice.id } } },
            });
        }
        return invoice;
    }
    async findAllInvoices(orderId, customerTaxNo, invoiceType, startDate, endDate) {
        return this.prisma.invoice.findMany({
            where: {
                orderId: orderId || undefined,
                customerTaxNo: customerTaxNo || undefined,
                invoiceType: invoiceType || undefined,
                createdAt: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
                deletedAt: null,
                isCancelled: false
            },
            include: { order: { select: { id: true, orderNumber: true, totalAmount: true } } },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneInvoice(id) {
        const invoice = await this.prisma.invoice.findUnique({
            where: { id, deletedAt: null },
            include: { order: { select: { id: true, orderNumber: true, totalAmount: true } } },
        });
        if (!invoice) {
            throw new common_1.NotFoundException(`Invoice with ID "${id}" not found.`);
        }
        return invoice;
    }
    async updateInvoice(id, data) {
        const existingInvoice = await this.findOneInvoice(id);
        if (existingInvoice.isCancelled) {
            throw new common_1.BadRequestException(`Cannot update a cancelled invoice.`);
        }
        if (data.serialNo || data.sequenceNo) {
            const newSerialNo = data.serialNo || existingInvoice.serialNo;
            const newSequenceNo = data.sequenceNo || existingInvoice.sequenceNo;
            const existingWithNewNumber = await this.prisma.invoice.findUnique({
                where: { serialNo_sequenceNo: { serialNo: newSerialNo, sequenceNo: newSequenceNo } },
            });
            if (existingWithNewNumber && existingWithNewNumber.id !== id) {
                throw new common_1.ConflictException(`Invoice with serial number "${newSerialNo}" and sequence number "${newSequenceNo}" already exists.`);
            }
        }
        if (data.isCancelled && !existingInvoice.isCancelled) {
            data.cancelledInvoiceId = data.cancelledInvoiceId || existingInvoice.id;
            data.cancelReason = data.cancelReason || 'Not specified';
            data.eArchiveStatus = 'CANCELLED';
        }
        try {
            return await this.prisma.invoice.update({
                where: { id, deletedAt: null },
                data: {
                    ...data,
                    subtotal: data.subtotal !== undefined ? parseFloat(data.subtotal.toFixed(2)) : undefined,
                    discountAmount: data.discountAmount !== undefined ? parseFloat(data.discountAmount.toFixed(2)) : undefined,
                    taxAmount: data.taxAmount !== undefined ? parseFloat(data.taxAmount.toFixed(2)) : undefined,
                    totalAmount: data.totalAmount !== undefined ? parseFloat(data.totalAmount.toFixed(2)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Invoice with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeInvoice(id) {
        try {
            return await this.prisma.invoice.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), isCancelled: true, eArchiveStatus: 'CANCELLED' },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Invoice with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.InvoiceService = InvoiceService;
exports.InvoiceService = InvoiceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], InvoiceService);
//# sourceMappingURL=invoice.service.js.map