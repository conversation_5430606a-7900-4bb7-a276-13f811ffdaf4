import { OnlineOrderService } from './online-order.service';
import { CreateOnlineOrderDto } from './dto/create-online-order.dto';
import { UpdateOnlineOrderDto } from './dto/update-online-order.dto';
declare enum OnlineOrderStatus {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED",
    PREPARING = "PREPARING",
    READY = "READY",
    DELIVERING = "DELIVERING",
    DELIVERED = "DELIVERED",
    CANCELLED = "CANCELLED",
    RETURNED = "RETURNED"
}
export declare class OnlineOrderController {
    private readonly onlineOrderService;
    constructor(onlineOrderService: OnlineOrderService);
    create(createOnlineOrderDto: CreateOnlineOrderDto): Promise<{
        paymentMethod: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        status: import("generated/prisma").$Enums.OnlineOrderStatus;
        customerName: string;
        customerPhone: string;
        deliveryAddress: string;
        deliveryNote: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderedAt: Date;
        preparingAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        platformOrderId: string;
        platformOrderNo: string;
        orderId: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        platformId: string;
        orderData: import("generated/prisma/runtime/library").JsonValue;
        platformStatus: string | null;
        serviceFee: import("generated/prisma/runtime/library").Decimal;
        discount: import("generated/prisma/runtime/library").Decimal;
        commissionAmount: import("generated/prisma/runtime/library").Decimal;
        netAmount: import("generated/prisma/runtime/library").Decimal;
        isPaid: boolean;
        requestedAt: Date | null;
        rejectReason: string | null;
        acceptedAt: Date | null;
        rejectedAt: Date | null;
        readyAt: Date | null;
        deliveringAt: Date | null;
    }>;
    findAll(platformId?: string, orderId?: string, status?: OnlineOrderStatus, platformOrderNo?: string, startDate?: Date, endDate?: Date): Promise<({
        order: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        } | null;
        platform: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        paymentMethod: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        status: import("generated/prisma").$Enums.OnlineOrderStatus;
        customerName: string;
        customerPhone: string;
        deliveryAddress: string;
        deliveryNote: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderedAt: Date;
        preparingAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        platformOrderId: string;
        platformOrderNo: string;
        orderId: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        platformId: string;
        orderData: import("generated/prisma/runtime/library").JsonValue;
        platformStatus: string | null;
        serviceFee: import("generated/prisma/runtime/library").Decimal;
        discount: import("generated/prisma/runtime/library").Decimal;
        commissionAmount: import("generated/prisma/runtime/library").Decimal;
        netAmount: import("generated/prisma/runtime/library").Decimal;
        isPaid: boolean;
        requestedAt: Date | null;
        rejectReason: string | null;
        acceptedAt: Date | null;
        rejectedAt: Date | null;
        readyAt: Date | null;
        deliveringAt: Date | null;
    })[]>;
    findOne(id: string): Promise<{
        order: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        } | null;
        platform: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        paymentMethod: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        status: import("generated/prisma").$Enums.OnlineOrderStatus;
        customerName: string;
        customerPhone: string;
        deliveryAddress: string;
        deliveryNote: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderedAt: Date;
        preparingAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        platformOrderId: string;
        platformOrderNo: string;
        orderId: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        platformId: string;
        orderData: import("generated/prisma/runtime/library").JsonValue;
        platformStatus: string | null;
        serviceFee: import("generated/prisma/runtime/library").Decimal;
        discount: import("generated/prisma/runtime/library").Decimal;
        commissionAmount: import("generated/prisma/runtime/library").Decimal;
        netAmount: import("generated/prisma/runtime/library").Decimal;
        isPaid: boolean;
        requestedAt: Date | null;
        rejectReason: string | null;
        acceptedAt: Date | null;
        rejectedAt: Date | null;
        readyAt: Date | null;
        deliveringAt: Date | null;
    }>;
    update(id: string, updateOnlineOrderDto: UpdateOnlineOrderDto): Promise<{
        paymentMethod: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        status: import("generated/prisma").$Enums.OnlineOrderStatus;
        customerName: string;
        customerPhone: string;
        deliveryAddress: string;
        deliveryNote: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderedAt: Date;
        preparingAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        platformOrderId: string;
        platformOrderNo: string;
        orderId: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        platformId: string;
        orderData: import("generated/prisma/runtime/library").JsonValue;
        platformStatus: string | null;
        serviceFee: import("generated/prisma/runtime/library").Decimal;
        discount: import("generated/prisma/runtime/library").Decimal;
        commissionAmount: import("generated/prisma/runtime/library").Decimal;
        netAmount: import("generated/prisma/runtime/library").Decimal;
        isPaid: boolean;
        requestedAt: Date | null;
        rejectReason: string | null;
        acceptedAt: Date | null;
        rejectedAt: Date | null;
        readyAt: Date | null;
        deliveringAt: Date | null;
    }>;
    remove(id: string): Promise<{
        paymentMethod: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        status: import("generated/prisma").$Enums.OnlineOrderStatus;
        customerName: string;
        customerPhone: string;
        deliveryAddress: string;
        deliveryNote: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        deliveryFee: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderedAt: Date;
        preparingAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        platformOrderId: string;
        platformOrderNo: string;
        orderId: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        platformId: string;
        orderData: import("generated/prisma/runtime/library").JsonValue;
        platformStatus: string | null;
        serviceFee: import("generated/prisma/runtime/library").Decimal;
        discount: import("generated/prisma/runtime/library").Decimal;
        commissionAmount: import("generated/prisma/runtime/library").Decimal;
        netAmount: import("generated/prisma/runtime/library").Decimal;
        isPaid: boolean;
        requestedAt: Date | null;
        rejectReason: string | null;
        acceptedAt: Date | null;
        rejectedAt: Date | null;
        readyAt: Date | null;
        deliveringAt: Date | null;
    }>;
}
export {};
