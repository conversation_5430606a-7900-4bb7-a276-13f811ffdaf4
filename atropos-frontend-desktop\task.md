
### Adım 1: Chakra UI v2 Kurulumu (Vite Uyumlu ve Doğru Versiyonlar)

**Talimat:**

`atropos-frontend-desktop` dizininde terminali açın ve aşağıdaki komutları çalıştırın. Bu, Chakra UI v2'nin Vite ile sorunsuz çalışması için gerekli tüm bağımlılıkları ve konfigürasyonu sağlayacaktır.

1.  **Chakra UI Paketlerini Kurun/Güncelleyin:**

    ```bash
    npm install @chakra-ui/react@^2.0.0 @emotion/react@^11.0.0 @emotion/styled@^11.0.0 framer-motion@^6.0.0
    # veya yarn kullanıyorsanız:
    # yarn add @chakra-ui/react@^2.0.0 @emotion/react@^11.0.0 @emotion/styled@^11.0.0 framer-motion@^6.0.0
    ```

    <PERSON>u paketler zaten daha önce bahsettiğ<PERSON>iz gibi Chakra UI v2'nin temel bağımlılıklarıdır.

2.  **`main.tsx`'e `ChakraProvider`'ı Ekleyin:**
    Bu adımda bir değişiklik yok, yine uygulamayı `ChakraProvider` ile sarmalayacağız.

    `atropos-frontend-desktop/src/main.tsx` dosyasını açın ve içeriğini aşağıdaki gibi güncelleyin:

    ```typescript
    // atropos-frontend-desktop/src/main.tsx
    import { StrictMode } from 'react';
    import { createRoot } from 'react-dom/client';
    import { ChakraProvider } from '@chakra-ui/react'; // ChakraProvider'ı import et
    import './index.css'; // Mevcut global stilleriniz
    import App from './App.tsx';

    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        {/* Uygulamayı ChakraProvider ile sarmala */}
        <ChakraProvider>
          <App />
        </ChakraProvider>
      </StrictMode>,
    );
    ```

3.  **`tsconfig.json` veya `tsconfig.app.json` Güncellemeleri (Vite Spesifik):**
    Chakra UI v2 dokümantasyonunda belirtildiği gibi, Emotion kütüphanelerinin JSX pragma'larını doğru bir şekilde tanıması için `tsconfig.json` veya `tsconfig.app.json` dosyasına bazı ayarlar eklememiz gerekiyor. `atropos-frontend-desktop` projesinde `tsconfig.app.json` dosyası mevcut ve bu, uygulama kodunuz için spesifik TypeScript ayarlarını içeriyor.

    **Talimat 3:**

    `atropos-frontend-desktop/tsconfig.app.json` dosyasını açın ve `compilerOptions` altına aşağıdaki satırları ekleyin:

    ```json
    {
      "compilerOptions": {
        // ... mevcut ayarlarınız
        "jsx": "react-jsx", // Zaten react-jsx olmalı, emin olun
        "jsxImportSource": "@emotion/react" // Bu satırı ekliyoruz
      },
      // ... diğer ayarlar
    }
    ```

    Genellikle `react-jsx` ile birlikte `jsxImportSource` otomatik olarak tanımlanır, ancak bu ayarı açıkça belirtmek, Chakra UI ve Emotion'ın doğru çalışmasını garantiler.

4.  **`App.css`'i Temizle (Vite entegrasyonuyla da geçerli):**
    Chakra UI'ın kendi sıfırlama (CSS reset) stillerini kullanmak ve bizim CSS'imizle çakışmaması için `App.css` dosyasını temizlemek hala en iyi uygulamadır.

    **Talimat 4:**

    `atropos-frontend-desktop/src/App.css` dosyasını açın ve tüm içeriğini aşağıdaki gibi minimal hale getirin:

    ```css
    /* atropos-frontend-desktop/src/App.css */

    #root {
      max-width: 1280px; /* Bu değer Figma tasarımınıza göre ayarlanabilir, merkezi konteyner için bir max-width */
      margin: 0 auto;
      padding: 0; /* Chakra kendi paddingini yönetecek */
      text-align: center;
      min-height: 100vh; /* Uygulamanın en az ekran yüksekliği kadar olmasını sağlar */
      display: flex; /* Flexbox ile içerik ortalama için */
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    body {
        margin: 0;
        /* Chakra UI kendi reset stillerini uygulayacağı için burada başka global stil tutmaya gerek yok. */
    }

    /* Eğer App bileşeni içinde hala '.App' sınıfını kullanıyorsak ve dış konteyner olarak işlev görüyorsa */
    .App {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    ```

Bu revize edilmiş adımlarla Chakra UI v2'yi Vite tabanlı frontend projenize doğru bir şekilde entegre etmiş olacağız.

Bu adımları tamamladığınızda bana haber verin.