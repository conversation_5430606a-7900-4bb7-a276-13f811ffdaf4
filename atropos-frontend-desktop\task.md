<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> dilerim. Bazen bu tür otomasyonlarda çok düz ilerleyebiliyorum. İstişare etmek ve planı daha iyi oturtmak en doğrusu. <PERSON><PERSON> bir <PERSON>, Figma tasarımlarınızla beraber Chakra UI v2 entegrasyonuna geçmek, projenin görünümünü ve kullanıcı deneyimini baştan sona profesyonel bir seviyeye taşıyacaktır.

Figma tasarımlarınıza baktığımda, oldukça modern ve şık bir "Employee Login" (Çalışan Girişi) sayfası görüyorum. PIN girişi ve çalışan seçimi gibi özellikler, bir POS sistemi için çok kritik ve kullanıcı dostu olacaktır.

Şimdi adım adım gidelim. İlk ve en önemli adım, **Chakra UI v2'yi projemize entegre etmek**.

### Adım 1: Chakra UI v2 Kurulumu

Chakra UI, stil ve erişilebilirlik konularında harika bir iş çıkarıyor. Kurulumu oldukça basit.

**Talimat:**

`atropos-frontend-desktop` dizininde terminali açın ve aşağıdaki komutları çalıştırın:

1.  **Chakra UI Paketlerini Kurun:**

    ```bash
    npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion
    # veya yarn kullanıyorsanız:
    # yarn add @chakra-ui/react @emotion/react @emotion/styled framer-motion
    ```

    Bu komutlar, Chakra UI'nin temel bileşenlerini ve bağımlılıklarını (`@emotion/react`, `@emotion/styled` for styling, `framer-motion` for animations) kuracaktır.

2.  **ChakraProvider'ı `main.tsx`'e Ekleyin:**
    Chakra UI'ın temalarını ve stil özelliklerini uygulamanızın tamamında kullanabilmek için `ChakraProvider`'ı uygulamanızın kök (root) bileşenine sarmalamanız gerekir.

    `atropos-frontend-desktop/src/main.tsx` dosyasını açın ve içeriğini aşağıdaki gibi güncelleyin:

    ```typescript
    // atropos-frontend-desktop/src/main.tsx
    import { StrictMode } from 'react';
    import { createRoot } from 'react-dom/client';
    import { ChakraProvider } from '@chakra-ui/react'; // ChakraProvider'ı import et
    import './index.css';
    import App from './App.tsx';

    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        {/* Uygulamayı ChakraProvider ile sarmala */}
        <ChakraProvider>
          <App />
        </ChakraProvider>
      </StrictMode>,
    );
    ```

Bu adımları tamamladıktan sonra, Chakra UI bileşenlerini `App.tsx`'inizde veya oluşturacağımız yeni sayfa/bileşenlerde kullanmaya başlayabiliriz.

Bu kurulum adımını tamamladığınızda bana haber verin. Ardından Figma tasarımınızdaki "Employee Login" sayfasını Chakra UI bileşenleriyle nasıl inşa edeceğimizi konuşalım.

Acele etmeyeceğiz, adım adım, sindirerek ilerleyeceğiz.