"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryItemService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let InventoryItemService = class InventoryItemService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createInventoryItem(data) {
        if (data.productId) {
            const productExists = await this.prisma.product.findUnique({
                where: { id: data.productId, deletedAt: null },
            });
            if (!productExists) {
                throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
            }
            const existingMappingToProduct = await this.prisma.inventoryItem.findFirst({
                where: { productId: data.productId, deletedAt: null }
            });
            if (existingMappingToProduct) {
                throw new common_1.ConflictException(`Inventory item already exists for product with ID "${data.productId}".`);
            }
        }
        const existingItemByCode = await this.prisma.inventoryItem.findUnique({
            where: { code: data.code, deletedAt: null },
        });
        if (existingItemByCode) {
            throw new common_1.ConflictException(`Inventory item with code "${data.code}" already exists.`);
        }
        if (data.barcode) {
            const existingItemByBarcode = await this.prisma.inventoryItem.findFirst({
                where: { barcode: data.barcode, deletedAt: null },
            });
            if (existingItemByBarcode) {
                throw new common_1.ConflictException(`Inventory item with barcode "${data.barcode}" already exists.`);
            }
        }
        const currentStock = data.currentStock !== undefined ? parseFloat(data.currentStock.toFixed(3)) : 0;
        const reservedStock = data.reservedStock !== undefined ? parseFloat(data.reservedStock.toFixed(3)) : 0;
        const availableStock = currentStock - reservedStock;
        return this.prisma.inventoryItem.create({
            data: {
                ...data,
                currentStock: currentStock,
                reservedStock: reservedStock,
                availableStock: parseFloat(availableStock.toFixed(3)),
                lastCost: data.lastCost !== undefined ? parseFloat(data.lastCost.toFixed(2)) : undefined,
                averageCost: data.averageCost !== undefined ? parseFloat(data.averageCost.toFixed(2)) : undefined,
                criticalLevel: data.criticalLevel !== undefined ? parseFloat(data.criticalLevel.toFixed(3)) : undefined,
                optimalLevel: data.optimalLevel !== undefined ? parseFloat(data.optimalLevel.toFixed(3)) : undefined,
            },
        });
    }
    async findAllInventoryItems(productId, code, supplier) {
        return this.prisma.inventoryItem.findMany({
            where: {
                productId: productId || undefined,
                code: code || undefined,
                supplier: supplier || undefined,
                deletedAt: null,
            },
            include: {
                product: { select: { id: true, name: true, code: true } },
            },
            orderBy: { name: 'asc' },
        });
    }
    async findOneInventoryItem(id) {
        const item = await this.prisma.inventoryItem.findUnique({
            where: { id, deletedAt: null },
            include: {
                product: { select: { id: true, name: true, code: true } },
            },
        });
        if (!item) {
            throw new common_1.NotFoundException(`Inventory item with ID "${id}" not found.`);
        }
        return item;
    }
    async updateInventoryItem(id, data) {
        const existingItem = await this.findOneInventoryItem(id);
        if (data.productId && data.productId !== existingItem.productId) {
            const productExists = await this.prisma.product.findUnique({
                where: { id: data.productId, deletedAt: null },
            });
            if (!productExists) {
                throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
            }
            const existingMappingToNewProduct = await this.prisma.inventoryItem.findFirst({
                where: { productId: data.productId, deletedAt: null }
            });
            if (existingMappingToNewProduct && existingMappingToNewProduct.id !== id) {
                throw new common_1.ConflictException(`Inventory item already exists for product with ID "${data.productId}".`);
            }
        }
        if (data.code && data.code !== existingItem.code) {
            const existingItemByCode = await this.prisma.inventoryItem.findUnique({
                where: { code: data.code, deletedAt: null },
            });
            if (existingItemByCode) {
                throw new common_1.ConflictException(`Inventory item with code "${data.code}" already exists.`);
            }
        }
        if (data.barcode && data.barcode !== existingItem.barcode) {
            const existingItemByBarcode = await this.prisma.inventoryItem.findFirst({
                where: { barcode: data.barcode, deletedAt: null },
            });
            if (existingItemByBarcode) {
                throw new common_1.ConflictException(`Inventory item with barcode "${data.barcode}" already exists.`);
            }
        }
        let updatedAvailableStock;
        if (data.currentStock !== undefined || data.reservedStock !== undefined) {
            const currentStock = data.currentStock !== undefined ? parseFloat(data.currentStock.toFixed(3)) : existingItem.currentStock.toNumber();
            const reservedStock = data.reservedStock !== undefined ? parseFloat(data.reservedStock.toFixed(3)) : existingItem.reservedStock.toNumber();
            updatedAvailableStock = currentStock - reservedStock;
        }
        try {
            return await this.prisma.inventoryItem.update({
                where: { id, deletedAt: null },
                data: {
                    ...data,
                    currentStock: data.currentStock !== undefined ? parseFloat(data.currentStock.toFixed(3)) : undefined,
                    reservedStock: data.reservedStock !== undefined ? parseFloat(data.reservedStock.toFixed(3)) : undefined,
                    availableStock: updatedAvailableStock !== undefined ? parseFloat(updatedAvailableStock.toFixed(3)) : undefined,
                    lastCost: data.lastCost !== undefined ? parseFloat(data.lastCost.toFixed(2)) : undefined,
                    averageCost: data.averageCost !== undefined ? parseFloat(data.averageCost.toFixed(2)) : undefined,
                    criticalLevel: data.criticalLevel !== undefined ? parseFloat(data.criticalLevel.toFixed(3)) : undefined,
                    optimalLevel: data.optimalLevel !== undefined ? parseFloat(data.optimalLevel.toFixed(3)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Inventory item with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeInventoryItem(id) {
        const recipeItemsCount = await this.prisma.recipeItem.count({
            where: { inventoryItemId: id }
        });
        if (recipeItemsCount > 0) {
            throw new common_1.ConflictException(`Inventory item with ID "${id}" cannot be deleted because it is used in ${recipeItemsCount} recipes.`);
        }
        const stockMovementsCount = await this.prisma.stockMovement.count({
            where: { inventoryItemId: id }
        });
        if (stockMovementsCount > 0) {
            throw new common_1.ConflictException(`Inventory item with ID "${id}" cannot be deleted because it has ${stockMovementsCount} associated stock movements.`);
        }
        try {
            return await this.prisma.inventoryItem.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Inventory item with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.InventoryItemService = InventoryItemService;
exports.InventoryItemService = InventoryItemService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], InventoryItemService);
//# sourceMappingURL=inventory-item.service.js.map