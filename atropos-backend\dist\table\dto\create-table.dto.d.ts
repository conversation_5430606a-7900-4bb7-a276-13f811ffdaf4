import { TableShape, TableStatus } from '../../../generated/prisma';
export declare class CreateTableDto {
    branchId: string;
    areaId?: string;
    number: string;
    name?: string;
    capacity?: number;
    minCapacity?: number;
    positionX?: number;
    positionY?: number;
    width?: number;
    height?: number;
    shape?: TableShape;
    status?: TableStatus;
    isVip?: boolean;
    qrCode?: string;
    active?: boolean;
}
