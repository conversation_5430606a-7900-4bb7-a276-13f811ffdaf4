"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let OrderService = class OrderService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async calculateOrderTotals(items) {
        let subtotal = 0;
        let taxAmount = 0;
        let totalAmount = 0;
        for (const item of items) {
            if (!item.unitPrice || !item.quantity || !item.taxRate)
                continue;
            const itemPrice = item.unitPrice * item.quantity;
            const itemDiscount = item.discountAmount || (item.discountRate ? (itemPrice * item.discountRate) / 100 : 0);
            const itemSubtotal = itemPrice - itemDiscount;
            const itemTax = (itemSubtotal * item.taxRate) / 100;
            const itemTotal = itemSubtotal + itemTax;
            subtotal += itemSubtotal;
            taxAmount += itemTax;
            totalAmount += itemTotal;
        }
        return {
            subtotal: parseFloat(subtotal.toFixed(2)),
            taxAmount: parseFloat(taxAmount.toFixed(2)),
            totalAmount: parseFloat(totalAmount.toFixed(2)),
        };
    }
    async createOrder(data) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        if (data.tableId) {
            const tableExists = await this.prisma.table.findUnique({
                where: { id: data.tableId, deletedAt: null },
            });
            if (!tableExists) {
                throw new common_1.NotFoundException(`Table with ID "${data.tableId}" not found.`);
            }
            await this.prisma.table.update({
                where: { id: data.tableId },
                data: { status: 'OCCUPIED' },
            });
        }
        if (data.customerId) {
            const customerExists = await this.prisma.customer.findUnique({
                where: { id: data.customerId, deletedAt: null },
            });
            if (!customerExists) {
                throw new common_1.NotFoundException(`Customer with ID "${data.customerId}" not found.`);
            }
        }
        if (data.waiterId) {
            const waiterExists = await this.prisma.user.findUnique({
                where: { id: data.waiterId, deletedAt: null, role: { in: ['WAITER', 'ADMIN', 'BRANCH_MANAGER'] } },
            });
            if (!waiterExists) {
                throw new common_1.NotFoundException(`Waiter with ID "${data.waiterId}" not found or unauthorized.`);
            }
        }
        for (const item of data.items) {
            const product = await this.prisma.product.findUnique({
                where: { id: item.productId, deletedAt: null },
                include: { tax: true, variants: true },
            });
            if (!product || !product.sellable) {
                throw new common_1.NotFoundException(`Product with ID "${item.productId}" not found or not sellable.`);
            }
            if (item.variantId) {
                const variant = product.variants.find(v => v.id === item.variantId);
                if (!variant) {
                    throw new common_1.NotFoundException(`Variant with ID "${item.variantId}" not found for product "${item.productId}".`);
                }
                item.unitPrice = variant.price.toNumber();
                item.costPrice = variant.costPrice?.toNumber();
            }
            else {
                item.unitPrice = product.basePrice.toNumber();
                item.costPrice = product.costPrice?.toNumber();
            }
            item.taxRate = product.tax.rate.toNumber();
        }
        const { subtotal, taxAmount, totalAmount } = await this.calculateOrderTotals(data.items);
        const currentYear = new Date().getFullYear();
        const latestOrder = await this.prisma.order.findFirst({
            where: { branchId: data.branchId, createdAt: { gte: new Date(`${currentYear}-01-01T00:00:00Z`) } },
            orderBy: { createdAt: 'desc' }
        });
        let orderNumber = `${currentYear}-000001`;
        if (latestOrder && latestOrder.orderNumber) {
            const lastNumber = parseInt(latestOrder.orderNumber.split('-')[1]);
            orderNumber = `${currentYear}-${String(lastNumber + 1).padStart(6, '0')}`;
        }
        const order = await this.prisma.order.create({
            data: {
                ...data,
                orderNumber,
                status: prisma_1.OrderStatus.PENDING,
                paymentStatus: 'UNPAID',
                subtotal,
                taxAmount,
                totalAmount,
                items: {
                    create: data.items.map(item => ({
                        productId: item.productId,
                        variantId: item.variantId,
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        costPrice: item.costPrice,
                        discountAmount: item.discountAmount || 0,
                        discountRate: item.discountRate || 0,
                        taxRate: item.taxRate,
                        taxAmount: (item.unitPrice * item.quantity * item.taxRate / 100),
                        totalAmount: (item.unitPrice * item.quantity) - (item.discountAmount || (item.discountRate ? (item.unitPrice * item.discountRate) / 100 : 0)) * (1 + item.taxRate / 100),
                        note: item.note,
                        guestName: item.guestName,
                        courseNumber: item.courseNumber,
                    })),
                },
            },
            include: { items: true },
        });
        return order;
    }
    async findAllOrders(branchId, tableId, customerId, status, paymentStatus, orderType, startDate, endDate) {
        return this.prisma.order.findMany({
            where: {
                branchId: branchId || undefined,
                tableId: tableId || undefined,
                customerId: customerId || undefined,
                status: status || undefined,
                paymentStatus: paymentStatus || undefined,
                orderType: orderType || undefined,
                orderedAt: {
                    gte: startDate || undefined,
                    lte: endDate || undefined,
                },
                deletedAt: null,
            },
            include: {
                branch: { select: { id: true, name: true } },
                table: { select: { id: true, number: true } },
                customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
                waiter: { select: { id: true, firstName: true, lastName: true } },
                items: {
                    include: { product: true, variant: true }
                }
            },
            orderBy: { orderedAt: 'desc' },
        });
    }
    async findOneOrder(id) {
        const order = await this.prisma.order.findUnique({
            where: { id, deletedAt: null },
            include: {
                branch: { select: { id: true, name: true } },
                table: { select: { id: true, number: true } },
                customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
                waiter: { select: { id: true, firstName: true, lastName: true } },
                courier: { select: { id: true, firstName: true, lastName: true } },
                onlinePlatform: { select: { id: true, name: true } },
                items: {
                    include: { product: true, variant: true }
                },
                payments: true,
            },
        });
        if (!order) {
            throw new common_1.NotFoundException(`Order with ID "${id}" not found.`);
        }
        return order;
    }
    async updateOrder(id, data) {
        const existingOrder = await this.findOneOrder(id);
        if (data.branchId && data.branchId !== existingOrder.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists)
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        let updatedOrderData = { ...data };
        let newItems = [];
        let itemsToUpdate = [];
        let itemIdsToDelete = [];
        if (data.items) {
            for (const item of data.items) {
                if (item.id) {
                    itemsToUpdate.push(item);
                }
                else {
                    newItems.push(item);
                }
            }
            const currentItems = await this.prisma.orderItem.findMany({ where: { orderId: id } });
            const currentItemIds = currentItems.map(i => i.id);
            const updatedItemIds = itemsToUpdate.map(i => i.id);
            itemIdsToDelete = currentItemIds.filter(itemId => !updatedItemIds.includes(itemId));
        }
        if (data.items) {
            const originalItems = await this.prisma.orderItem.findMany({ where: { orderId: id, id: { notIn: itemIdsToDelete } } });
            const processedItems = new Map();
            originalItems.forEach(item => {
                processedItems.set(item.id, {
                    ...item,
                    quantity: item.quantity.toNumber(),
                    unitPrice: item.unitPrice.toNumber(),
                    discountAmount: item.discountAmount.toNumber(),
                    discountRate: item.discountRate.toNumber(),
                    taxRate: item.taxRate.toNumber(),
                    costPrice: item.costPrice?.toNumber()
                });
            });
            itemsToUpdate.forEach(item => {
                if (!item.id)
                    return;
                const existing = processedItems.get(item.id);
                if (existing) {
                    processedItems.set(item.id, {
                        ...existing,
                        ...item,
                        quantity: item.quantity !== undefined ? parseFloat(item.quantity.toFixed(3)) : existing.quantity,
                        unitPrice: item.unitPrice !== undefined ? parseFloat(item.unitPrice.toFixed(2)) : existing.unitPrice,
                        discountAmount: item.discountAmount !== undefined ? parseFloat(item.discountAmount.toFixed(2)) : existing.discountAmount,
                        discountRate: item.discountRate !== undefined ? parseFloat(item.discountRate.toFixed(2)) : existing.discountRate,
                        taxRate: item.taxRate !== undefined ? parseFloat(item.taxRate.toFixed(2)) : existing.taxRate,
                        costPrice: item.costPrice !== undefined ? parseFloat(item.costPrice.toFixed(2)) : existing.costPrice,
                    });
                }
            });
            newItems.forEach(item => {
                processedItems.set(item.productId + (item.variantId || ''), item);
            });
            const { subtotal, taxAmount, totalAmount } = await this.calculateOrderTotals(Array.from(processedItems.values()));
            updatedOrderData.subtotal = subtotal;
            updatedOrderData.taxAmount = taxAmount;
            updatedOrderData.totalAmount = totalAmount;
        }
        const transaction = [];
        for (const item of itemsToUpdate) {
            if (!item.id)
                continue;
            transaction.push(this.prisma.orderItem.update({
                where: { id: item.id },
                data: {
                    productId: item.productId,
                    variantId: item.variantId,
                    quantity: item.quantity !== undefined ? parseFloat(item.quantity.toFixed(3)) : undefined,
                    unitPrice: item.unitPrice !== undefined ? parseFloat(item.unitPrice.toFixed(2)) : undefined,
                    costPrice: item.costPrice !== undefined ? parseFloat(item.costPrice.toFixed(2)) : undefined,
                    discountAmount: item.discountAmount !== undefined ? parseFloat(item.discountAmount.toFixed(2)) : undefined,
                    discountRate: item.discountRate !== undefined ? parseFloat(item.discountRate.toFixed(2)) : undefined,
                    taxRate: item.taxRate !== undefined ? parseFloat(item.taxRate.toFixed(2)) : undefined,
                    taxAmount: item.unitPrice && item.quantity && item.taxRate ? (item.unitPrice * item.quantity * item.taxRate / 100) : undefined,
                    totalAmount: item.unitPrice && item.quantity && item.taxRate ? ((item.unitPrice * item.quantity) - (item.discountAmount || (item.discountRate ? (item.unitPrice * item.discountRate) / 100 : 0))) * (1 + item.taxRate / 100) : undefined,
                    note: item.note,
                    guestName: item.guestName,
                    courseNumber: item.courseNumber,
                }
            }));
        }
        if (newItems.length > 0) {
            transaction.push(this.prisma.orderItem.createMany({
                data: newItems.map(item => ({
                    orderId: id,
                    productId: item.productId,
                    variantId: item.variantId,
                    quantity: parseFloat(item.quantity.toFixed(3)),
                    unitPrice: parseFloat(item.unitPrice.toFixed(2)),
                    costPrice: item.costPrice !== undefined ? parseFloat(item.costPrice.toFixed(2)) : undefined,
                    discountAmount: item.discountAmount !== undefined ? parseFloat(item.discountAmount.toFixed(2)) : 0,
                    discountRate: item.discountRate !== undefined ? parseFloat(item.discountRate.toFixed(2)) : 0,
                    taxRate: parseFloat(item.taxRate.toFixed(2)),
                    taxAmount: (item.unitPrice * item.quantity * item.taxRate / 100),
                    totalAmount: ((item.unitPrice * item.quantity) - (item.discountAmount || (item.discountRate ? (item.unitPrice * item.discountRate) / 100 : 0))) * (1 + item.taxRate / 100),
                    note: item.note,
                    guestName: item.guestName,
                    courseNumber: item.courseNumber,
                }))
            }));
        }
        if (itemIdsToDelete.length > 0) {
            transaction.push(this.prisma.orderItem.deleteMany({
                where: { id: { in: itemIdsToDelete } }
            }));
        }
        if (transaction.length > 0) {
            await this.prisma.$transaction(transaction);
        }
        if (data.status && ['COMPLETED', 'CANCELLED', 'RETURNED'].includes(data.status) && existingOrder.tableId) {
            await this.prisma.table.update({
                where: { id: existingOrder.tableId },
                data: { status: 'EMPTY' },
            });
        }
        try {
            return await this.prisma.order.update({
                where: { id, deletedAt: null },
                data: updatedOrderData,
                include: { items: true },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Order with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeOrder(id) {
        try {
            const order = await this.prisma.order.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), status: 'CANCELLED' },
            });
            if (order.tableId) {
                await this.prisma.table.update({
                    where: { id: order.tableId },
                    data: { status: 'EMPTY' },
                });
            }
            return order;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Order with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.OrderService = OrderService;
exports.OrderService = OrderService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrderService);
//# sourceMappingURL=order.service.js.map