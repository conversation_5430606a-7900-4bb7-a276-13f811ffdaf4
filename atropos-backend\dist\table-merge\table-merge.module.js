"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableMergeModule = void 0;
const common_1 = require("@nestjs/common");
const table_merge_service_1 = require("./table-merge.service");
const table_merge_controller_1 = require("./table-merge.controller");
const prisma_module_1 = require("../prisma/prisma.module");
let TableMergeModule = class TableMergeModule {
};
exports.TableMergeModule = TableMergeModule;
exports.TableMergeModule = TableMergeModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [table_merge_controller_1.TableMergeController],
        providers: [table_merge_service_1.TableMergeService],
        exports: [table_merge_service_1.TableMergeService],
    })
], TableMergeModule);
//# sourceMappingURL=table-merge.module.js.map