"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AuditLogService = class AuditLogService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createAuditLog(data) {
        if (data.userId) {
            const userExists = await this.prisma.user.findUnique({
                where: { id: data.userId, deletedAt: null },
            });
            if (!userExists) {
                throw new common_1.NotFoundException(`User with ID "${data.userId}" not found for audit log.`);
            }
        }
        return this.prisma.auditLog.create({
            data: {
                ...data,
                timestamp: new Date(),
            },
        });
    }
    async findAllAuditLogs(userId, action, entityType, entityId, startDate, endDate) {
        return this.prisma.auditLog.findMany({
            where: {
                userId: userId || undefined,
                action: action || undefined,
                entityType: entityType || undefined,
                entityId: entityId || undefined,
                timestamp: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
            },
            include: {
                user: { select: { id: true, username: true, firstName: true, lastName: true } },
            },
            orderBy: { timestamp: 'desc' },
        });
    }
    async findOneAuditLog(id) {
        const log = await this.prisma.auditLog.findUnique({
            where: { id },
            include: {
                user: { select: { id: true, username: true, firstName: true, lastName: true } },
            },
        });
        if (!log) {
            throw new common_1.NotFoundException(`Audit log with ID "${id}" not found.`);
        }
        return log;
    }
    async updateAuditLog(id, data) {
        throw new common_1.ForbiddenException('Audit logs cannot be updated.');
    }
    async removeAuditLog(id) {
        throw new common_1.ForbiddenException('Audit logs cannot be deleted.');
    }
};
exports.AuditLogService = AuditLogService;
exports.AuditLogService = AuditLogService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AuditLogService);
//# sourceMappingURL=audit-log.service.js.map