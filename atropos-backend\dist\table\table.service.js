"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let TableService = class TableService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createTable(data) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        if (data.areaId) {
            const areaExists = await this.prisma.tableArea.findUnique({
                where: { id: data.areaId, deletedAt: null },
            });
            if (!areaExists) {
                throw new common_1.NotFoundException(`Table area with ID "${data.areaId}" not found.`);
            }
        }
        const existingTable = await this.prisma.table.findUnique({
            where: {
                branchId_number: {
                    branchId: data.branchId,
                    number: data.number,
                },
            },
        });
        if (existingTable) {
            throw new common_1.ConflictException(`Table with number "${data.number}" already exists for this branch.`);
        }
        if (data.qrCode) {
            const existingQrCode = await this.prisma.table.findUnique({
                where: { qrCode: data.qrCode, deletedAt: null }
            });
            if (existingQrCode) {
                throw new common_1.ConflictException(`QR Code "${data.qrCode}" is already in use by another table.`);
            }
        }
        return this.prisma.table.create({ data });
    }
    async findAllTables(branchId, areaId, status) {
        return this.prisma.table.findMany({
            where: {
                branchId: branchId || undefined,
                areaId: areaId || undefined,
                status: status ? status : undefined,
                deletedAt: null,
            },
            include: {
                branch: { select: { id: true, name: true } },
                area: { select: { id: true, name: true } },
                orders: {
                    where: { deletedAt: null, completedAt: null },
                    select: { id: true, orderNumber: true, status: true, totalAmount: true }
                }
            },
            orderBy: { number: 'asc' },
        });
    }
    async findOneTable(id) {
        const table = await this.prisma.table.findUnique({
            where: { id, deletedAt: null },
            include: {
                branch: { select: { id: true, name: true } },
                area: { select: { id: true, name: true } },
                orders: {
                    where: { deletedAt: null, completedAt: null },
                    select: { id: true, orderNumber: true, status: true, totalAmount: true }
                }
            },
        });
        if (!table) {
            throw new common_1.NotFoundException(`Table with ID "${id}" not found.`);
        }
        return table;
    }
    async updateTable(id, data) {
        if (data.branchId) {
            const branchExists = await this.prisma.branch.findUnique({
                where: { id: data.branchId, deletedAt: null },
            });
            if (!branchExists) {
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
            }
        }
        if (data.areaId) {
            const areaExists = await this.prisma.tableArea.findUnique({
                where: { id: data.areaId, deletedAt: null },
            });
            if (!areaExists) {
                throw new common_1.NotFoundException(`Table area with ID "${data.areaId}" not found.`);
            }
        }
        if (data.number) {
            const currentTable = await this.findOneTable(id);
            const existingTable = await this.prisma.table.findUnique({
                where: {
                    branchId_number: {
                        branchId: data.branchId || currentTable.branchId,
                        number: data.number,
                    },
                },
            });
            if (existingTable && existingTable.id !== id) {
                throw new common_1.ConflictException(`Table with number "${data.number}" already exists for this branch.`);
            }
        }
        if (data.qrCode) {
            const existingQrCode = await this.prisma.table.findUnique({
                where: { qrCode: data.qrCode, deletedAt: null }
            });
            if (existingQrCode && existingQrCode.id !== id) {
                throw new common_1.ConflictException(`QR Code "${data.qrCode}" is already in use by another table.`);
            }
        }
        try {
            return await this.prisma.table.update({
                where: { id, deletedAt: null },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Table with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeTable(id) {
        const activeOrdersCount = await this.prisma.order.count({
            where: { tableId: id, status: { notIn: ['COMPLETED', 'CANCELLED', 'RETURNED'] }, deletedAt: null }
        });
        if (activeOrdersCount > 0) {
            throw new common_1.ConflictException(`Table with ID "${id}" cannot be deleted because it has ${activeOrdersCount} active orders.`);
        }
        try {
            return await this.prisma.table.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false, status: 'UNAVAILABLE' },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Table with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.TableService = TableService;
exports.TableService = TableService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TableService);
//# sourceMappingURL=table.service.js.map