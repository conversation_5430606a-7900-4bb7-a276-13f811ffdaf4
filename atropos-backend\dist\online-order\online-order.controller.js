"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineOrderController = void 0;
const common_1 = require("@nestjs/common");
const online_order_service_1 = require("./online-order.service");
const create_online_order_dto_1 = require("./dto/create-online-order.dto");
const update_online_order_dto_1 = require("./dto/update-online-order.dto");
var OnlineOrderStatus;
(function (OnlineOrderStatus) {
    OnlineOrderStatus["PENDING"] = "PENDING";
    OnlineOrderStatus["ACCEPTED"] = "ACCEPTED";
    OnlineOrderStatus["REJECTED"] = "REJECTED";
    OnlineOrderStatus["PREPARING"] = "PREPARING";
    OnlineOrderStatus["READY"] = "READY";
    OnlineOrderStatus["DELIVERING"] = "DELIVERING";
    OnlineOrderStatus["DELIVERED"] = "DELIVERED";
    OnlineOrderStatus["CANCELLED"] = "CANCELLED";
    OnlineOrderStatus["RETURNED"] = "RETURNED";
})(OnlineOrderStatus || (OnlineOrderStatus = {}));
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
const parse_optional_enum_pipe_1 = require("../common/pipes/parse-optional-enum.pipe");
let OnlineOrderController = class OnlineOrderController {
    onlineOrderService;
    constructor(onlineOrderService) {
        this.onlineOrderService = onlineOrderService;
    }
    create(createOnlineOrderDto) {
        return this.onlineOrderService.createOnlineOrder(createOnlineOrderDto);
    }
    findAll(platformId, orderId, status, platformOrderNo, startDate, endDate) {
        return this.onlineOrderService.findAllOnlineOrders(platformId, orderId, status, platformOrderNo, startDate, endDate);
    }
    findOne(id) {
        return this.onlineOrderService.findOneOnlineOrder(id);
    }
    update(id, updateOnlineOrderDto) {
        return this.onlineOrderService.updateOnlineOrder(id, updateOnlineOrderDto);
    }
    remove(id) {
        return this.onlineOrderService.removeOnlineOrder(id);
    }
};
exports.OnlineOrderController = OnlineOrderController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_online_order_dto_1.CreateOnlineOrderDto]),
    __metadata("design:returntype", void 0)
], OnlineOrderController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('platformId')),
    __param(1, (0, common_1.Query)('orderId')),
    __param(2, (0, common_1.Query)('status', new parse_optional_enum_pipe_1.ParseOptionalEnumPipe(OnlineOrderStatus))),
    __param(3, (0, common_1.Query)('platformOrderNo')),
    __param(4, (0, common_1.Query)('startDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __param(5, (0, common_1.Query)('endDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Date,
        Date]),
    __metadata("design:returntype", void 0)
], OnlineOrderController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlineOrderController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_online_order_dto_1.UpdateOnlineOrderDto]),
    __metadata("design:returntype", void 0)
], OnlineOrderController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlineOrderController.prototype, "remove", null);
exports.OnlineOrderController = OnlineOrderController = __decorate([
    (0, common_1.Controller)('online-order'),
    __metadata("design:paramtypes", [online_order_service_1.OnlineOrderService])
], OnlineOrderController);
//# sourceMappingURL=online-order.controller.js.map