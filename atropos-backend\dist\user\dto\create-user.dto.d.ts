import { UserRole } from '../../../generated/prisma';
export declare class CreateUserDto {
    companyId: string;
    branchId?: string;
    username: string;
    password: string;
    pin?: string;
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    avatar?: string;
    role: UserRole;
    employeeCode?: string;
    hireDate?: Date;
    birthDate?: Date;
    nationalId?: string;
    vehicleType?: string;
    vehiclePlate?: string;
    active?: boolean;
}
