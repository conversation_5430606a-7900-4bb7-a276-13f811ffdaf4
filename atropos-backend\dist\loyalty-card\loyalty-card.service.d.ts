import { PrismaService } from '../prisma/prisma.service';
import { CreateLoyaltyCardDto } from './dto/create-loyalty-card.dto';
import { UpdateLoyaltyCardDto } from './dto/update-loyalty-card.dto';
export declare class LoyaltyCardService {
    private prisma;
    constructor(prisma: PrismaService);
    createLoyaltyCard(data: CreateLoyaltyCardDto): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    findAllLoyaltyCards(customerId?: string, cardNumber?: string, cardType?: string): Promise<{
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        };
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }[]>;
    findOneLoyaltyCard(id: string): Promise<{
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        };
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    updateLoyaltyCard(id: string, data: UpdateLoyaltyCardDto): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    removeLoyaltyCard(id: string): Promise<{
        id: string;
        active: boolean;
        blocked: boolean;
        blockReason: string | null;
    }>;
    addPoints(cardId: string, pointsToAdd: number): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    spendPoints(cardId: string, pointsToSpend: number): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    addBalance(cardId: string, amountToAdd: number): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    spendBalance(cardId: string, amountToSpend: number): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    blockCard(cardId: string, reason: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    unblockCard(cardId: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    activateCard(cardId: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    deactivateCard(cardId: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
}
