{"version": 3, "file": "online-product-mapping.service.js", "sourceRoot": "", "sources": ["../../src/online-product-mapping/online-product-mapping.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,2CAAkF;AAClF,6DAAyD;AAKlD,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IAClB;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,0BAA0B,CAAC,IAAmC;QAElE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;YACjE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,IAAI,CAAC,UAAU,cAAc,CAAC,CAAC;QACzF,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;SAC/C,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,IAAI,CAAC,SAAS,cAAc,CAAC,CAAC;QAChF,CAAC;QAGD,MAAM,0BAA0B,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;YACnF,KAAK,EAAE;gBACL,oBAAoB,EAAE;oBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC1B;aACF;SACF,CAAC,CAAC;QACH,IAAI,0BAA0B,EAAE,CAAC;YAC/B,MAAM,IAAI,0BAAiB,CAAC,+CAA+C,IAAI,CAAC,SAAS,kBAAkB,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QAClI,CAAC;QAGD,MAAM,kCAAkC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAC3F,KAAK,EAAE;gBACL,4BAA4B,EAAE;oBAC5B,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;iBAC1C;aACF;SACF,CAAC,CAAC;QACH,IAAI,kCAAkC,EAAE,CAAC;YACvC,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,IAAI,CAAC,iBAAiB,oCAAoC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QACrI,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAC7C,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,aAAa,EAAE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;aACxG;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,UAAmB,EAAE,SAAkB;QACxE,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YAC/C,KAAK,EAAE;gBACL,UAAU,EAAE,UAAU,IAAI,SAAS;gBACnC,SAAS,EAAE,SAAS,IAAI,SAAS;aAClC;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBAC1D,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE;aAC3E;YACD,OAAO,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;SACxB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,EAAU;QAC1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAChE,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBAC1D,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE;aAC3E;SACF,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,EAAE,cAAc,CAAC,CAAC;QACnF,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,EAAU,EAAE,IAAmC;QAE9E,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACvG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,IAAI,CAAC,UAAU,cAAc,CAAC,CAAC;YAC3F,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAC/G,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,IAAI,CAAC,SAAS,cAAc,CAAC,CAAC;YAClF,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC9D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;YAClE,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC;YAGtE,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS,EAAE,CAAC;gBAChE,MAAM,0BAA0B,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACjF,KAAK,EAAE,EAAE,oBAAoB,EAAE,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE;iBAC/F,CAAC,CAAC;gBACH,IAAI,0BAA0B,IAAI,0BAA0B,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACrE,MAAM,IAAI,0BAAiB,CAAC,+CAA+C,IAAI,CAAC,SAAS,kBAAkB,gBAAgB,IAAI,CAAC,CAAC;gBACrI,CAAC;YACL,CAAC;YAGD,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,KAAK,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBACxF,MAAM,kCAAkC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACzF,KAAK,EAAE,EAAE,4BAA4B,EAAE,EAAE,UAAU,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE;iBACvH,CAAC,CAAC;gBACH,IAAI,kCAAkC,IAAI,kCAAkC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACrF,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,IAAI,CAAC,iBAAiB,oCAAoC,gBAAgB,IAAI,CAAC,CAAC;gBACxI,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,IAAI,EAAE;oBACF,GAAG,IAAI;oBACP,aAAa,EAAE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC1G;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,EAAE,cAAc,CAAC,CAAC;YACnF,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,EAAU;QAEzC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,EAAE,cAAc,CAAC,CAAC;YACnF,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAxJY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,2BAA2B,CAwJvC"}