"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParseOptionalDatePipe = void 0;
const common_1 = require("@nestjs/common");
let ParseOptionalDatePipe = class ParseOptionalDatePipe {
    transform(value, metadata) {
        if (!value) {
            return undefined;
        }
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            throw new common_1.BadRequestException(`Validation failed (ISO 8601 date string is expected for ${metadata.data}).`);
        }
        return date;
    }
};
exports.ParseOptionalDatePipe = ParseOptionalDatePipe;
exports.ParseOptionalDatePipe = ParseOptionalDatePipe = __decorate([
    (0, common_1.Injectable)()
], ParseOptionalDatePipe);
//# sourceMappingURL=parse-optional-date.pipe.js.map