import { StockMovementService } from './stock-movement.service';
import { CreateStockMovementDto } from './dto/create-stock-movement.dto';
import { UpdateStockMovementDto } from './dto/update-stock-movement.dto';
import { StockMovementType } from '../../generated/prisma';
export declare class StockMovementController {
    private readonly stockMovementService;
    constructor(stockMovementService: StockMovementService);
    create(createStockMovementDto: CreateStockMovementDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        type: import("../../generated/prisma").$Enums.StockMovementType;
        unit: import("../../generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("generated/prisma/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("generated/prisma/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("generated/prisma/runtime/library").Decimal | null;
        previousCost: import("generated/prisma/runtime/library").Decimal | null;
        newAverageCost: import("generated/prisma/runtime/library").Decimal | null;
        previousStock: import("generated/prisma/runtime/library").Decimal;
    }>;
    findAll(branchId?: string, productId?: string, inventoryItemId?: string, type?: StockMovementType, startDate?: string, endDate?: string): Promise<({
        branch: {
            name: string;
            id: string;
        };
        product: {
            name: string;
            id: string;
            code: string;
        } | null;
        inventoryItem: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        type: import("../../generated/prisma").$Enums.StockMovementType;
        unit: import("../../generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("generated/prisma/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("generated/prisma/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("generated/prisma/runtime/library").Decimal | null;
        previousCost: import("generated/prisma/runtime/library").Decimal | null;
        newAverageCost: import("generated/prisma/runtime/library").Decimal | null;
        previousStock: import("generated/prisma/runtime/library").Decimal;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        product: {
            name: string;
            id: string;
            code: string;
        } | null;
        inventoryItem: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        type: import("../../generated/prisma").$Enums.StockMovementType;
        unit: import("../../generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("generated/prisma/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("generated/prisma/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("generated/prisma/runtime/library").Decimal | null;
        previousCost: import("generated/prisma/runtime/library").Decimal | null;
        newAverageCost: import("generated/prisma/runtime/library").Decimal | null;
        previousStock: import("generated/prisma/runtime/library").Decimal;
    }>;
    update(id: string, updateStockMovementDto: UpdateStockMovementDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        type: import("../../generated/prisma").$Enums.StockMovementType;
        unit: import("../../generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("generated/prisma/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("generated/prisma/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("generated/prisma/runtime/library").Decimal | null;
        previousCost: import("generated/prisma/runtime/library").Decimal | null;
        newAverageCost: import("generated/prisma/runtime/library").Decimal | null;
        previousStock: import("generated/prisma/runtime/library").Decimal;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        type: import("../../generated/prisma").$Enums.StockMovementType;
        unit: import("../../generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("generated/prisma/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("generated/prisma/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("generated/prisma/runtime/library").Decimal | null;
        previousCost: import("generated/prisma/runtime/library").Decimal | null;
        newAverageCost: import("generated/prisma/runtime/library").Decimal | null;
        previousStock: import("generated/prisma/runtime/library").Decimal;
    }>;
}
