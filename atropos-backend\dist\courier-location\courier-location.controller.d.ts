import { CourierLocationService } from './courier-location.service';
import { CreateCourierLocationDto } from './dto/create-courier-location.dto';
import { UpdateCourierLocationDto } from './dto/update-courier-location.dto';
export declare class CourierLocationController {
    private readonly courierLocationService;
    constructor(courierLocationService: CourierLocationService);
    create(createCourierLocationDto: CreateCourierLocationDto): Promise<{
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
    findAll(courierId?: string, branchId?: string, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
        courier: {
            id: string;
            firstName: string;
            lastName: string;
            employeeCode: string | null;
        };
    } & {
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        courier: {
            id: string;
            firstName: string;
            lastName: string;
            employeeCode: string | null;
        };
    } & {
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
    update(id: string, updateCourierLocationDto: UpdateCourierLocationDto): Promise<{
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
    remove(id: string): Promise<{
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
}
