"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePrinterDto = void 0;
const class_validator_1 = require("class-validator");
var PrinterType;
(function (PrinterType) {
    PrinterType["THERMAL"] = "THERMAL";
    PrinterType["DOT_MATRIX"] = "DOT_MATRIX";
    PrinterType["A4"] = "A4";
})(PrinterType || (PrinterType = {}));
class CreatePrinterDto {
    branchId;
    printerGroupId;
    name;
    type;
    connectionType;
    ipAddress;
    port;
    active;
}
exports.CreatePrinterDto = CreatePrinterDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePrinterDto.prototype, "branchId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePrinterDto.prototype, "printerGroupId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePrinterDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PrinterType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePrinterDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePrinterDto.prototype, "connectionType", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIP)('4'),
    __metadata("design:type", String)
], CreatePrinterDto.prototype, "ipAddress", void 0);
__decorate([
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(65535),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreatePrinterDto.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreatePrinterDto.prototype, "active", void 0);
//# sourceMappingURL=create-printer.dto.js.map