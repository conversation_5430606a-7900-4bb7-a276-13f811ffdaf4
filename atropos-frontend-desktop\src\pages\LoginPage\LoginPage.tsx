// atropos-frontend-desktop/src/pages/LoginPage/LoginPage.tsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  VStack,
  Heading,
  useToast,
  Center,
} from '@chakra-ui/react';
// Chakra UI kullandığımız için artık App.css'i burada doğrudan import etmiyoruz.
// Genel stiller main.tsx veya App.css'te global olarak yönetilebilir.

interface LoginResponse {
  accessToken: string;
}

function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const toast = useToast();

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'; //

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch(`${API_URL}/auth/login`, { //
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data: LoginResponse & { message?: string } = await response.json();

      if (response.ok) {
        localStorage.setItem('accessToken', data.accessToken);
        console.log('Access Token:', data.accessToken);
        toast({
          title: 'Giriş Başarılı.',
          description: 'Panele yönlendiriliyorsunuz.',
          status: 'success',
          duration: 2000,
          isClosable: true,
        });
        navigate('/dashboard');
      } else {
        toast({
          title: 'Giriş Başarısız.',
          description: data.message || 'Kullanıcı adı veya parola hatalı.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Login sırasında hata oluştu:', error);
      toast({
        title: 'Bağlantı Hatası.',
        description: 'Sunucuya ulaşılamıyor veya ağ bağlantınızda sorun var.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center minH="100vh" bg="gray.50">
      <Box
        p={8}
        maxWidth="400px"
        borderWidth={1}
        borderRadius="lg"
        boxShadow="lg"
        bg="white"
      >
        <VStack spacing={6}>
          <Heading as="h1" size="lg" mb={4}>
            Atropos POS - Giriş
          </Heading>
          <form onSubmit={handleLogin} style={{ width: '100%' }}>
            <VStack spacing={4}>
              <FormControl id="username">
                <FormLabel>Kullanıcı Adı</FormLabel>
                <Input
                  type="text"
                  placeholder="Kullanıcı adınızı girin"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  isDisabled={isLoading}
                />
              </FormControl>
              <FormControl id="password">
                <FormLabel>Parola</FormLabel>
                <Input
                  type="password"
                  placeholder="Parolanızı girin"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  isDisabled={isLoading}
                />
              </FormControl>
              <Button
                type="submit"
                colorScheme="blue"
                width="full"
                isLoading={isLoading}
                loadingText="Giriş Yapılıyor..."
              >
                Giriş Yap
              </Button>
            </VStack>
          </form>
        </VStack>
      </Box>
    </Center>
  );
}

export default LoginPage;
