import { PrismaService } from '../prisma/prisma.service';
import { CreateOnlineProductMappingDto } from './dto/create-online-product-mapping.dto';
import { UpdateOnlineProductMappingDto } from './dto/update-online-product-mapping.dto';
export declare class OnlineProductMappingService {
    private prisma;
    constructor(prisma: PrismaService);
    createOnlineProductMapping(data: CreateOnlineProductMappingDto): Promise<{
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
    findAllOnlineProductMappings(platformId?: string, productId?: string): Promise<({
        product: {
            name: string;
            id: string;
            code: string;
            basePrice: import("generated/prisma/runtime/library").Decimal;
        };
        platform: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    })[]>;
    findOneOnlineProductMapping(id: string): Promise<{
        product: {
            name: string;
            id: string;
            code: string;
            basePrice: import("generated/prisma/runtime/library").Decimal;
        };
        platform: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
    updateOnlineProductMapping(id: string, data: UpdateOnlineProductMappingDto): Promise<{
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
    removeOnlineProductMapping(id: string): Promise<{
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
}
