import { PrismaService } from '../prisma/prisma.service';
import { CreateCashMovementDto } from './dto/create-cash-movement.dto';
import { UpdateCashMovementDto } from './dto/update-cash-movement.dto';
import { CashMovementType } from '../../generated/prisma';
export declare class CashMovementService {
    private prisma;
    constructor(prisma: PrismaService);
    createCashMovement(data: CreateCashMovementDto): Promise<{
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import("../../generated/prisma").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("generated/prisma/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("generated/prisma/runtime/library").Decimal;
        currentBalance: import("generated/prisma/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
    findAllCashMovements(branchId?: string, userId?: string, type?: CashMovementType, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
        user: {
            id: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import("../../generated/prisma").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("generated/prisma/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("generated/prisma/runtime/library").Decimal;
        currentBalance: import("generated/prisma/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    })[]>;
    findOneCashMovement(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        user: {
            id: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import("../../generated/prisma").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("generated/prisma/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("generated/prisma/runtime/library").Decimal;
        currentBalance: import("generated/prisma/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
    updateCashMovement(id: string, data: UpdateCashMovementDto): Promise<{
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import("../../generated/prisma").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("generated/prisma/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("generated/prisma/runtime/library").Decimal;
        currentBalance: import("generated/prisma/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
    removeCashMovement(id: string): Promise<{
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import("../../generated/prisma").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("generated/prisma/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("generated/prisma/runtime/library").Decimal;
        currentBalance: import("generated/prisma/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
}
