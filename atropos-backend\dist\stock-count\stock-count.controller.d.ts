import { StockCountService } from './stock-count.service';
import { CreateStockCountDto } from './dto/create-stock-count.dto';
import { UpdateStockCountDto } from './dto/update-stock-count.dto';
import { StockCountType, StockCountStatus } from '../../generated/prisma';
export declare class StockCountController {
    private readonly stockCountService;
    constructor(stockCountService: StockCountService);
    create(createStockCountDto: CreateStockCountDto): Promise<{
        items: {
            id: string;
            note: string | null;
            difference: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("generated/prisma/runtime/library").Decimal | null;
            countedQuantity: import("generated/prisma/runtime/library").Decimal;
            systemQuantity: import("generated/prisma/runtime/library").Decimal;
            totalDifference: import("generated/prisma/runtime/library").Decimal | null;
            stockCountId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
    findAll(branchId?: string, countType?: StockCountType, status?: StockCountStatus, startDate?: string, endDate?: string): Promise<({} & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    })[]>;
    findOne(id: string): Promise<{
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("../../generated/prisma").$Enums.ProductUnit;
                currentStock: import("generated/prisma/runtime/library").Decimal;
                averageCost: import("generated/prisma/runtime/library").Decimal | null;
            };
        } & {
            id: string;
            note: string | null;
            difference: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("generated/prisma/runtime/library").Decimal | null;
            countedQuantity: import("generated/prisma/runtime/library").Decimal;
            systemQuantity: import("generated/prisma/runtime/library").Decimal;
            totalDifference: import("generated/prisma/runtime/library").Decimal | null;
            stockCountId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
    update(id: string, updateStockCountDto: UpdateStockCountDto): Promise<{
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("../../generated/prisma").$Enums.ProductUnit;
                currentStock: import("generated/prisma/runtime/library").Decimal;
                averageCost: import("generated/prisma/runtime/library").Decimal | null;
            };
        } & {
            id: string;
            note: string | null;
            difference: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("generated/prisma/runtime/library").Decimal | null;
            countedQuantity: import("generated/prisma/runtime/library").Decimal;
            systemQuantity: import("generated/prisma/runtime/library").Decimal;
            totalDifference: import("generated/prisma/runtime/library").Decimal | null;
            stockCountId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
}
