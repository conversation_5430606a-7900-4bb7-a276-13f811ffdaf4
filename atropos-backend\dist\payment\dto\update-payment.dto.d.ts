import { CreatePaymentDto } from './create-payment.dto';
import { PaymentStatus } from '../../../generated/prisma';
export declare class RefundPaymentDto {
    refundAmount: number;
    refundReason: string;
    refundedBy?: string;
}
declare const UpdatePaymentDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreatePaymentDto>>;
export declare class UpdatePaymentDto extends UpdatePaymentDto_base {
    status?: PaymentStatus;
    refundAmount?: number;
    refundReason?: string;
    refundedAt?: Date;
}
export {};
