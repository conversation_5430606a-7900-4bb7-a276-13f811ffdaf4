import {CalendarDate as $35ea8db9cb2ccb90$export$99faa760c7908e4f} from "./CalendarDate.mjs";
import {GregorianCalendar as $3b62074eb05584b2$export$80ee6245ec4f29ec} from "./GregorianCalendar.mjs";

/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ // Portions of the code in this file are based on code from the TC39 Temporal proposal.
// Original licensing can be found in the NOTICE file in the root directory of this source tree.


const $62225008020f0a13$var$ERA_START_DATES = [
    [
        1868,
        9,
        8
    ],
    [
        1912,
        7,
        30
    ],
    [
        1926,
        12,
        25
    ],
    [
        1989,
        1,
        8
    ],
    [
        2019,
        5,
        1
    ]
];
const $62225008020f0a13$var$ERA_END_DATES = [
    [
        1912,
        7,
        29
    ],
    [
        1926,
        12,
        24
    ],
    [
        1989,
        1,
        7
    ],
    [
        2019,
        4,
        30
    ]
];
const $62225008020f0a13$var$ERA_ADDENDS = [
    1867,
    1911,
    1925,
    1988,
    2018
];
const $62225008020f0a13$var$ERA_NAMES = [
    'meiji',
    'taisho',
    'showa',
    'heisei',
    'reiwa'
];
function $62225008020f0a13$var$findEraFromGregorianDate(date) {
    const idx = $62225008020f0a13$var$ERA_START_DATES.findIndex(([year, month, day])=>{
        if (date.year < year) return true;
        if (date.year === year && date.month < month) return true;
        if (date.year === year && date.month === month && date.day < day) return true;
        return false;
    });
    if (idx === -1) return $62225008020f0a13$var$ERA_START_DATES.length - 1;
    if (idx === 0) return 0;
    return idx - 1;
}
function $62225008020f0a13$var$toGregorian(date) {
    let eraAddend = $62225008020f0a13$var$ERA_ADDENDS[$62225008020f0a13$var$ERA_NAMES.indexOf(date.era)];
    if (!eraAddend) throw new Error('Unknown era: ' + date.era);
    return new (0, $35ea8db9cb2ccb90$export$99faa760c7908e4f)(date.year + eraAddend, date.month, date.day);
}
class $62225008020f0a13$export$b746ab2b60cdffbf extends (0, $3b62074eb05584b2$export$80ee6245ec4f29ec) {
    fromJulianDay(jd) {
        let date = super.fromJulianDay(jd);
        let era = $62225008020f0a13$var$findEraFromGregorianDate(date);
        return new (0, $35ea8db9cb2ccb90$export$99faa760c7908e4f)(this, $62225008020f0a13$var$ERA_NAMES[era], date.year - $62225008020f0a13$var$ERA_ADDENDS[era], date.month, date.day);
    }
    toJulianDay(date) {
        return super.toJulianDay($62225008020f0a13$var$toGregorian(date));
    }
    balanceDate(date) {
        let gregorianDate = $62225008020f0a13$var$toGregorian(date);
        let era = $62225008020f0a13$var$findEraFromGregorianDate(gregorianDate);
        if ($62225008020f0a13$var$ERA_NAMES[era] !== date.era) {
            date.era = $62225008020f0a13$var$ERA_NAMES[era];
            date.year = gregorianDate.year - $62225008020f0a13$var$ERA_ADDENDS[era];
        }
        // Constrain in case we went before the first supported era.
        this.constrainDate(date);
    }
    constrainDate(date) {
        let idx = $62225008020f0a13$var$ERA_NAMES.indexOf(date.era);
        let end = $62225008020f0a13$var$ERA_END_DATES[idx];
        if (end != null) {
            let [endYear, endMonth, endDay] = end;
            // Constrain the year to the maximum possible value in the era.
            // Then constrain the month and day fields within that.
            let maxYear = endYear - $62225008020f0a13$var$ERA_ADDENDS[idx];
            date.year = Math.max(1, Math.min(maxYear, date.year));
            if (date.year === maxYear) {
                date.month = Math.min(endMonth, date.month);
                if (date.month === endMonth) date.day = Math.min(endDay, date.day);
            }
        }
        if (date.year === 1 && idx >= 0) {
            let [, startMonth, startDay] = $62225008020f0a13$var$ERA_START_DATES[idx];
            date.month = Math.max(startMonth, date.month);
            if (date.month === startMonth) date.day = Math.max(startDay, date.day);
        }
    }
    getEras() {
        return $62225008020f0a13$var$ERA_NAMES;
    }
    getYearsInEra(date) {
        // Get the number of years in the era, taking into account the date's month and day fields.
        let era = $62225008020f0a13$var$ERA_NAMES.indexOf(date.era);
        let cur = $62225008020f0a13$var$ERA_START_DATES[era];
        let next = $62225008020f0a13$var$ERA_START_DATES[era + 1];
        if (next == null) // 9999 gregorian is the maximum year allowed.
        return 9999 - cur[0] + 1;
        let years = next[0] - cur[0];
        if (date.month < next[1] || date.month === next[1] && date.day < next[2]) years++;
        return years;
    }
    getDaysInMonth(date) {
        return super.getDaysInMonth($62225008020f0a13$var$toGregorian(date));
    }
    getMinimumMonthInYear(date) {
        let start = $62225008020f0a13$var$getMinimums(date);
        return start ? start[1] : 1;
    }
    getMinimumDayInMonth(date) {
        let start = $62225008020f0a13$var$getMinimums(date);
        return start && date.month === start[1] ? start[2] : 1;
    }
    constructor(...args){
        super(...args), this.identifier = 'japanese';
    }
}
function $62225008020f0a13$var$getMinimums(date) {
    if (date.year === 1) {
        let idx = $62225008020f0a13$var$ERA_NAMES.indexOf(date.era);
        return $62225008020f0a13$var$ERA_START_DATES[idx];
    }
}


export {$62225008020f0a13$export$b746ab2b60cdffbf as JapaneseCalendar};
//# sourceMappingURL=JapaneseCalendar.module.js.map
