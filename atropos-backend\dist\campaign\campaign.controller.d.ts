import { CampaignService } from './campaign.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignType } from '../../generated/prisma';
export declare class CampaignController {
    private readonly campaignService;
    constructor(campaignService: CampaignService);
    create(createCampaignDto: CreateCampaignDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import("../../generated/prisma").$Enums.CampaignType;
        discountType: import("../../generated/prisma").$Enums.DiscountType | null;
        discountValue: import("generated/prisma/runtime/library").Decimal | null;
        minOrderAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxDiscountAmount: import("generated/prisma/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
    findAll(companyId?: string, campaignType?: CampaignType, active?: boolean): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import("../../generated/prisma").$Enums.CampaignType;
        discountType: import("../../generated/prisma").$Enums.DiscountType | null;
        discountValue: import("generated/prisma/runtime/library").Decimal | null;
        minOrderAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxDiscountAmount: import("generated/prisma/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import("../../generated/prisma").$Enums.CampaignType;
        discountType: import("../../generated/prisma").$Enums.DiscountType | null;
        discountValue: import("generated/prisma/runtime/library").Decimal | null;
        minOrderAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxDiscountAmount: import("generated/prisma/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
    update(id: string, updateCampaignDto: UpdateCampaignDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import("../../generated/prisma").$Enums.CampaignType;
        discountType: import("../../generated/prisma").$Enums.DiscountType | null;
        discountValue: import("generated/prisma/runtime/library").Decimal | null;
        minOrderAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxDiscountAmount: import("generated/prisma/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import("../../generated/prisma").$Enums.CampaignType;
        discountType: import("../../generated/prisma").$Enums.DiscountType | null;
        discountValue: import("generated/prisma/runtime/library").Decimal | null;
        minOrderAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxDiscountAmount: import("generated/prisma/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
}
