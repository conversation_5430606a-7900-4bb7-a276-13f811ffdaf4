"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableMergeController = void 0;
const common_1 = require("@nestjs/common");
const table_merge_service_1 = require("./table-merge.service");
const create_table_merge_dto_1 = require("./dto/create-table-merge.dto");
const update_table_merge_dto_1 = require("./dto/update-table-merge.dto");
let TableMergeController = class TableMergeController {
    tableMergeService;
    constructor(tableMergeService) {
        this.tableMergeService = tableMergeService;
    }
    create(createTableMergeDto) {
        return this.tableMergeService.createTableMerge(createTableMergeDto);
    }
    findAll(tableId, targetId) {
        return this.tableMergeService.findAllTableMerges(tableId, targetId);
    }
    findOne(id) {
        return this.tableMergeService.findOneTableMerge(id);
    }
    update(id, updateTableMergeDto) {
        return this.tableMergeService.updateTableMerge(id, updateTableMergeDto);
    }
    remove(id) {
        return this.tableMergeService.removeTableMerge(id);
    }
};
exports.TableMergeController = TableMergeController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_table_merge_dto_1.CreateTableMergeDto]),
    __metadata("design:returntype", void 0)
], TableMergeController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('tableId')),
    __param(1, (0, common_1.Query)('targetId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], TableMergeController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TableMergeController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_table_merge_dto_1.UpdateTableMergeDto]),
    __metadata("design:returntype", void 0)
], TableMergeController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TableMergeController.prototype, "remove", null);
exports.TableMergeController = TableMergeController = __decorate([
    (0, common_1.Controller)('table-merge'),
    __metadata("design:paramtypes", [table_merge_service_1.TableMergeService])
], TableMergeController);
//# sourceMappingURL=table-merge.controller.js.map