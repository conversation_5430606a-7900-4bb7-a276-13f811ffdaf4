"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyTransactionModule = void 0;
const common_1 = require("@nestjs/common");
const loyalty_transaction_service_1 = require("./loyalty-transaction.service");
const loyalty_transaction_controller_1 = require("./loyalty-transaction.controller");
const prisma_module_1 = require("../prisma/prisma.module");
const loyalty_card_module_1 = require("../loyalty-card/loyalty-card.module");
let LoyaltyTransactionModule = class LoyaltyTransactionModule {
};
exports.LoyaltyTransactionModule = LoyaltyTransactionModule;
exports.LoyaltyTransactionModule = LoyaltyTransactionModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, loyalty_card_module_1.LoyaltyCardModule],
        controllers: [loyalty_transaction_controller_1.LoyaltyTransactionController],
        providers: [loyalty_transaction_service_1.LoyaltyTransactionService],
        exports: [loyalty_transaction_service_1.LoyaltyTransactionService],
    })
], LoyaltyTransactionModule);
//# sourceMappingURL=loyalty-transaction.module.js.map