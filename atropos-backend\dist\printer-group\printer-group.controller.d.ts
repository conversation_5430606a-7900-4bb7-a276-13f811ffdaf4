import { PrinterGroupService } from './printer-group.service';
import { CreatePrinterGroupDto } from './dto/create-printer-group.dto';
import { UpdatePrinterGroupDto } from './dto/update-printer-group.dto';
export declare class PrinterGroupController {
    private readonly printerGroupService;
    constructor(printerGroupService: PrinterGroupService);
    create(createPrinterGroupDto: CreatePrinterGroupDto): Promise<{
        categories: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    }>;
    findAll(): Promise<({
        categories: {
            name: string;
            id: string;
        }[];
        printers: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            active: boolean;
            branchId: string;
            printerGroupId: string | null;
            type: import("generated/prisma").$Enums.PrinterType;
            connectionType: string;
            ipAddress: string | null;
            port: number | null;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    })[]>;
    findOne(id: string): Promise<{
        categories: {
            name: string;
            id: string;
        }[];
        printers: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            active: boolean;
            branchId: string;
            printerGroupId: string | null;
            type: import("generated/prisma").$Enums.PrinterType;
            connectionType: string;
            ipAddress: string | null;
            port: number | null;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    }>;
    update(id: string, updatePrinterGroupDto: UpdatePrinterGroupDto): Promise<{
        categories: {
            name: string;
            id: string;
        }[];
    } & {
        name: string;
        id: string;
        categoryIds: string[];
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        categoryIds: string[];
    }>;
}
