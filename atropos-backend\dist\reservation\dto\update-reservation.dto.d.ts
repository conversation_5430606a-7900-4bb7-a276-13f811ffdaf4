import { CreateReservationDto } from './create-reservation.dto';
import { ReservationStatus } from '../../../generated/prisma';
declare const UpdateReservationDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateReservationDto>>;
export declare class UpdateReservationDto extends UpdateReservationDto_base {
    confirmedAt?: Date;
    cancelledAt?: Date;
    seatedAt?: Date;
    completedAt?: Date;
    reminderSentAt?: Date;
    reminderSent?: boolean;
    status?: ReservationStatus;
}
export {};
