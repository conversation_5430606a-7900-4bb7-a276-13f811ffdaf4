import { ProductUnit } from '../../../generated/prisma';
export declare class CreateProductDto {
    companyId: string;
    categoryId: string;
    code: string;
    barcode?: string;
    name: string;
    description?: string;
    shortDescription?: string;
    image?: string;
    images?: string[];
    basePrice: number;
    taxId: string;
    costPrice?: number;
    profitMargin?: number;
    trackStock?: boolean;
    unit: ProductUnit;
    criticalStock?: number;
    available?: boolean;
    sellable?: boolean;
    preparationTime?: number;
    calories?: number;
    allergens?: string[];
    hasVariants?: boolean;
    hasModifiers?: boolean;
    showInMenu?: boolean;
    featured?: boolean;
    displayOrder?: number;
    active?: boolean;
}
