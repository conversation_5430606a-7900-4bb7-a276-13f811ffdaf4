import { PriceOverrideService } from './price-override.service';
import { CreatePriceOverrideDto } from './dto/create-price-override.dto';
import { UpdatePriceOverrideDto } from './dto/update-price-override.dto';
export declare class PriceOverrideController {
    private readonly priceOverrideService;
    constructor(priceOverrideService: PriceOverrideService);
    create(createPriceOverrideDto: CreatePriceOverrideDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
    findAll(branchId?: string, productId?: string, variantId?: string, activeOnly?: boolean, date?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
        product: {
            name: string;
            id: string;
            code: string;
        };
        variant: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        product: {
            name: string;
            id: string;
            code: string;
        };
        variant: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
    update(id: string, updatePriceOverrideDto: UpdatePriceOverrideDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
}
