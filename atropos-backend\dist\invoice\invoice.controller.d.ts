import { InvoiceService } from './invoice.service';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
declare const InvoiceType: {
    readonly RECEIPT: "RECEIPT";
    readonly INVOICE: "INVOICE";
    readonly E_ARCHIVE: "E_ARCHIVE";
    readonly E_INVOICE: "E_INVOICE";
    readonly PROFORMA: "PROFORMA";
    readonly RETURN: "RETURN";
};
type InvoiceType = typeof InvoiceType[keyof typeof InvoiceType];
export declare class InvoiceController {
    private readonly invoiceService;
    constructor(invoiceService: InvoiceService);
    create(createInvoiceDto: CreateInvoiceDto): Promise<{
        customerAddress: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        customerName: string | null;
        customerPhone: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string | null;
        printedAt: Date | null;
        invoiceType: import("generated/prisma").$Enums.InvoiceType;
        serialNo: string;
        sequenceNo: string;
        taxDetails: import("generated/prisma/runtime/library").JsonValue;
        totalAmountText: string | null;
        uuid: string | null;
        eArchiveStatus: import("generated/prisma").$Enums.EArchiveStatus | null;
        eArchiveResponse: import("generated/prisma/runtime/library").JsonValue | null;
        isCancelled: boolean;
        cancelReason: string | null;
        cancelledInvoiceId: string | null;
        pdfUrl: string | null;
        sentAt: Date | null;
        viewedAt: Date | null;
        customerTaxNo: string | null;
        customerTaxOffice: string | null;
        customerEmail: string | null;
    }>;
    findAll(orderId?: string, customerTaxNo?: string, invoiceType?: InvoiceType, startDate?: Date, endDate?: Date): Promise<({
        order: {
            id: string;
            orderNumber: string;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        } | null;
    } & {
        customerAddress: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        customerName: string | null;
        customerPhone: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string | null;
        printedAt: Date | null;
        invoiceType: import("generated/prisma").$Enums.InvoiceType;
        serialNo: string;
        sequenceNo: string;
        taxDetails: import("generated/prisma/runtime/library").JsonValue;
        totalAmountText: string | null;
        uuid: string | null;
        eArchiveStatus: import("generated/prisma").$Enums.EArchiveStatus | null;
        eArchiveResponse: import("generated/prisma/runtime/library").JsonValue | null;
        isCancelled: boolean;
        cancelReason: string | null;
        cancelledInvoiceId: string | null;
        pdfUrl: string | null;
        sentAt: Date | null;
        viewedAt: Date | null;
        customerTaxNo: string | null;
        customerTaxOffice: string | null;
        customerEmail: string | null;
    })[]>;
    findOne(id: string): Promise<{
        order: {
            id: string;
            orderNumber: string;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        } | null;
    } & {
        customerAddress: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        customerName: string | null;
        customerPhone: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string | null;
        printedAt: Date | null;
        invoiceType: import("generated/prisma").$Enums.InvoiceType;
        serialNo: string;
        sequenceNo: string;
        taxDetails: import("generated/prisma/runtime/library").JsonValue;
        totalAmountText: string | null;
        uuid: string | null;
        eArchiveStatus: import("generated/prisma").$Enums.EArchiveStatus | null;
        eArchiveResponse: import("generated/prisma/runtime/library").JsonValue | null;
        isCancelled: boolean;
        cancelReason: string | null;
        cancelledInvoiceId: string | null;
        pdfUrl: string | null;
        sentAt: Date | null;
        viewedAt: Date | null;
        customerTaxNo: string | null;
        customerTaxOffice: string | null;
        customerEmail: string | null;
    }>;
    update(id: string, updateInvoiceDto: UpdateInvoiceDto): Promise<{
        customerAddress: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        customerName: string | null;
        customerPhone: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string | null;
        printedAt: Date | null;
        invoiceType: import("generated/prisma").$Enums.InvoiceType;
        serialNo: string;
        sequenceNo: string;
        taxDetails: import("generated/prisma/runtime/library").JsonValue;
        totalAmountText: string | null;
        uuid: string | null;
        eArchiveStatus: import("generated/prisma").$Enums.EArchiveStatus | null;
        eArchiveResponse: import("generated/prisma/runtime/library").JsonValue | null;
        isCancelled: boolean;
        cancelReason: string | null;
        cancelledInvoiceId: string | null;
        pdfUrl: string | null;
        sentAt: Date | null;
        viewedAt: Date | null;
        customerTaxNo: string | null;
        customerTaxOffice: string | null;
        customerEmail: string | null;
    }>;
    remove(id: string): Promise<{
        customerAddress: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        customerName: string | null;
        customerPhone: string | null;
        subtotal: import("generated/prisma/runtime/library").Decimal;
        discountAmount: import("generated/prisma/runtime/library").Decimal;
        taxAmount: import("generated/prisma/runtime/library").Decimal;
        totalAmount: import("generated/prisma/runtime/library").Decimal;
        orderId: string | null;
        printedAt: Date | null;
        invoiceType: import("generated/prisma").$Enums.InvoiceType;
        serialNo: string;
        sequenceNo: string;
        taxDetails: import("generated/prisma/runtime/library").JsonValue;
        totalAmountText: string | null;
        uuid: string | null;
        eArchiveStatus: import("generated/prisma").$Enums.EArchiveStatus | null;
        eArchiveResponse: import("generated/prisma/runtime/library").JsonValue | null;
        isCancelled: boolean;
        cancelReason: string | null;
        cancelledInvoiceId: string | null;
        pdfUrl: string | null;
        sentAt: Date | null;
        viewedAt: Date | null;
        customerTaxNo: string | null;
        customerTaxOffice: string | null;
        customerEmail: string | null;
    }>;
}
export {};
