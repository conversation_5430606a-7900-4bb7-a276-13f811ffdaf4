import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface TreeViewNodeCheckboxBaseProps extends PolymorphicProps {
}
export interface TreeViewNodeCheckboxProps extends HTMLProps<'span'>, TreeViewNodeCheckboxBaseProps {
}
export declare const TreeViewNodeCheckbox: ForwardRefExoticComponent<TreeViewNodeCheckboxProps & RefAttributes<HTMLSpanElement>>;
