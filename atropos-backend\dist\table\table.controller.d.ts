import { TableService } from './table.service';
import { CreateTableDto } from './dto/create-table.dto';
import { UpdateTableDto } from './dto/update-table.dto';
export declare class TableController {
    private readonly tableService;
    constructor(tableService: TableService);
    create(createTableDto: CreateTableDto): Promise<{
        number: string;
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        areaId: string | null;
        capacity: number;
        minCapacity: number;
        positionX: number | null;
        positionY: number | null;
        width: number | null;
        height: number | null;
        shape: import("generated/prisma").$Enums.TableShape;
        status: import("generated/prisma").$Enums.TableStatus;
        isVip: boolean;
        qrCode: string | null;
        mergedWithIds: string[];
    }>;
    findAll(branchId?: string, areaId?: string, status?: string): Promise<({
        branch: {
            name: string;
            id: string;
        };
        orders: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        }[];
        area: {
            name: string;
            id: string;
        } | null;
    } & {
        number: string;
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        areaId: string | null;
        capacity: number;
        minCapacity: number;
        positionX: number | null;
        positionY: number | null;
        width: number | null;
        height: number | null;
        shape: import("generated/prisma").$Enums.TableShape;
        status: import("generated/prisma").$Enums.TableStatus;
        isVip: boolean;
        qrCode: string | null;
        mergedWithIds: string[];
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        orders: {
            id: string;
            status: import("generated/prisma").$Enums.OrderStatus;
            orderNumber: string;
            totalAmount: import("generated/prisma/runtime/library").Decimal;
        }[];
        area: {
            name: string;
            id: string;
        } | null;
    } & {
        number: string;
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        areaId: string | null;
        capacity: number;
        minCapacity: number;
        positionX: number | null;
        positionY: number | null;
        width: number | null;
        height: number | null;
        shape: import("generated/prisma").$Enums.TableShape;
        status: import("generated/prisma").$Enums.TableStatus;
        isVip: boolean;
        qrCode: string | null;
        mergedWithIds: string[];
    }>;
    update(id: string, updateTableDto: UpdateTableDto): Promise<{
        number: string;
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        areaId: string | null;
        capacity: number;
        minCapacity: number;
        positionX: number | null;
        positionY: number | null;
        width: number | null;
        height: number | null;
        shape: import("generated/prisma").$Enums.TableShape;
        status: import("generated/prisma").$Enums.TableStatus;
        isVip: boolean;
        qrCode: string | null;
        mergedWithIds: string[];
    }>;
    remove(id: string): Promise<{
        number: string;
        name: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        areaId: string | null;
        capacity: number;
        minCapacity: number;
        positionX: number | null;
        positionY: number | null;
        width: number | null;
        height: number | null;
        shape: import("generated/prisma").$Enums.TableShape;
        status: import("generated/prisma").$Enums.TableStatus;
        isVip: boolean;
        qrCode: string | null;
        mergedWithIds: string[];
    }>;
}
