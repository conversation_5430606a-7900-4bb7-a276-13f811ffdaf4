"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ProductService = class ProductService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createProduct(data) {
        const categoryExists = await this.prisma.category.findUnique({
            where: { id: data.categoryId, deletedAt: null },
        });
        if (!categoryExists) {
            throw new common_1.NotFoundException(`Category with ID "${data.categoryId}" not found.`);
        }
        const taxExists = await this.prisma.tax.findUnique({
            where: { id: data.taxId, deletedAt: null },
        });
        if (!taxExists) {
            throw new common_1.NotFoundException(`Tax with ID "${data.taxId}" not found.`);
        }
        const existingProductByCode = await this.prisma.product.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingProductByCode) {
            throw new common_1.ConflictException(`Product with code "${data.code}" already exists for this company.`);
        }
        if (data.barcode) {
            const existingProductByBarcode = await this.prisma.product.findFirst({
                where: { barcode: data.barcode, deletedAt: null },
            });
            if (existingProductByBarcode) {
                throw new common_1.ConflictException(`Product with barcode "${data.barcode}" already exists.`);
            }
        }
        return this.prisma.product.create({
            data: {
                ...data,
                basePrice: parseFloat(data.basePrice.toFixed(2)),
                costPrice: data.costPrice !== undefined ? parseFloat(data.costPrice.toFixed(2)) : undefined,
                profitMargin: data.profitMargin !== undefined ? parseFloat(data.profitMargin.toFixed(2)) : undefined,
                criticalStock: data.criticalStock !== undefined ? parseFloat(data.criticalStock.toFixed(3)) : undefined,
            },
        });
    }
    async findAllProducts(companyId, categoryId) {
        return this.prisma.product.findMany({
            where: {
                companyId: companyId || undefined,
                categoryId: categoryId || undefined,
                deletedAt: null,
            },
            include: {
                category: { select: { id: true, name: true } },
                tax: { select: { id: true, name: true, rate: true } },
            },
            orderBy: { name: 'asc' },
        });
    }
    async findOneProduct(id) {
        const product = await this.prisma.product.findUnique({
            where: { id, deletedAt: null },
            include: {
                category: { select: { id: true, name: true } },
                tax: { select: { id: true, name: true, rate: true } },
                variants: true,
                modifierGroups: true,
                recipes: true,
            },
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with ID "${id}" not found.`);
        }
        return product;
    }
    async updateProduct(id, data) {
        if (data.categoryId) {
            const categoryExists = await this.prisma.category.findUnique({
                where: { id: data.categoryId, deletedAt: null },
            });
            if (!categoryExists) {
                throw new common_1.NotFoundException(`Category with ID "${data.categoryId}" not found.`);
            }
        }
        if (data.taxId) {
            const taxExists = await this.prisma.tax.findUnique({
                where: { id: data.taxId, deletedAt: null },
            });
            if (!taxExists) {
                throw new common_1.NotFoundException(`Tax with ID "${data.taxId}" not found.`);
            }
        }
        if (data.code) {
            const currentProduct = await this.findOneProduct(id);
            const existingProductByCode = await this.prisma.product.findUnique({
                where: {
                    companyId_code: {
                        companyId: data.companyId || currentProduct.companyId,
                        code: data.code,
                    },
                },
            });
            if (existingProductByCode && existingProductByCode.id !== id) {
                throw new common_1.ConflictException(`Product with code "${data.code}" already exists for this company.`);
            }
        }
        if (data.barcode) {
            const existingProductByBarcode = await this.prisma.product.findFirst({
                where: { barcode: data.barcode, id: { not: id }, deletedAt: null },
            });
            if (existingProductByBarcode) {
                throw new common_1.ConflictException(`Product with barcode "${data.barcode}" already exists for another product.`);
            }
        }
        try {
            return await this.prisma.product.update({
                where: { id, deletedAt: null },
                data: {
                    ...data,
                    basePrice: data.basePrice !== undefined ? parseFloat(data.basePrice.toFixed(2)) : undefined,
                    costPrice: data.costPrice !== undefined ? parseFloat(data.costPrice.toFixed(2)) : undefined,
                    profitMargin: data.profitMargin !== undefined ? parseFloat(data.profitMargin.toFixed(2)) : undefined,
                    criticalStock: data.criticalStock !== undefined ? parseFloat(data.criticalStock.toFixed(3)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Product with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeProduct(id) {
        const orderItemsCount = await this.prisma.orderItem.count({
            where: { productId: id }
        });
        if (orderItemsCount > 0) {
            throw new common_1.ConflictException(`Product with ID "${id}" cannot be deleted because it has ${orderItemsCount} associated order items.`);
        }
        const variantsCount = await this.prisma.productVariant.count({
            where: { productId: id }
        });
        if (variantsCount > 0) {
            throw new common_1.ConflictException(`Product with ID "${id}" cannot be deleted because it has ${variantsCount} associated variants.`);
        }
        try {
            return await this.prisma.product.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false, sellable: false, available: false, showInMenu: false, featured: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Product with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.ProductService = ProductService;
exports.ProductService = ProductService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ProductService);
//# sourceMappingURL=product.service.js.map