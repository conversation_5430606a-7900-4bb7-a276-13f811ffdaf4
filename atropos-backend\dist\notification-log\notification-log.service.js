"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationLogService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let NotificationLogService = class NotificationLogService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createNotificationLog(data) {
        const templateExists = await this.prisma.notificationTemplate.findUnique({
            where: { id: data.templateId },
        });
        if (!templateExists) {
            throw new common_1.NotFoundException(`Notification template with ID "${data.templateId}" not found.`);
        }
        return this.prisma.notificationLog.create({
            data: {
                ...data,
                status: data.status || prisma_1.NotificationStatus.PENDING,
                sentAt: new Date(),
            },
        });
    }
    async findAllNotificationLogs(templateId, recipient, channel, status, startDate, endDate) {
        return this.prisma.notificationLog.findMany({
            where: {
                templateId: templateId || undefined,
                recipient: recipient || undefined,
                channel: channel || undefined,
                status: status || undefined,
                sentAt: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
            },
            include: {
                template: { select: { id: true, name: true, code: true, channel: true } },
            },
            orderBy: { sentAt: 'desc' },
        });
    }
    async findOneNotificationLog(id) {
        const log = await this.prisma.notificationLog.findUnique({
            where: { id },
            include: {
                template: { select: { id: true, name: true, code: true, channel: true } },
            },
        });
        if (!log) {
            throw new common_1.NotFoundException(`Notification log with ID "${id}" not found.`);
        }
        return log;
    }
    async updateNotificationLog(id, data) {
        const existingLog = await this.findOneNotificationLog(id);
        if (data.templateId !== undefined || data.recipient !== undefined || data.channel !== undefined ||
            data.message !== undefined) {
            throw new common_1.ForbiddenException('Cannot update core fields of a notification log. Only status, timestamps, and failedReason can be updated.');
        }
        try {
            return await this.prisma.notificationLog.update({
                where: { id },
                data: {
                    status: data.status || undefined,
                    sentAt: data.sentAt || undefined,
                    deliveredAt: data.deliveredAt || undefined,
                    readAt: data.readAt || undefined,
                    failedReason: data.failedReason || undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Notification log with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeNotificationLog(id) {
        try {
            return await this.prisma.notificationLog.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Notification log with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.NotificationLogService = NotificationLogService;
exports.NotificationLogService = NotificationLogService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], NotificationLogService);
//# sourceMappingURL=notification-log.service.js.map