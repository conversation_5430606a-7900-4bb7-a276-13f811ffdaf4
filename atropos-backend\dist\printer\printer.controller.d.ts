import { PrinterService } from './printer.service';
import { CreatePrinterDto } from './dto/create-printer.dto';
import { UpdatePrinterDto } from './dto/update-printer.dto';
declare enum PrinterType {
    THERMAL = "THERMAL",
    DOT_MATRIX = "DOT_MATRIX",
    A4 = "A4"
}
export declare class PrinterController {
    private readonly printerService;
    constructor(printerService: PrinterService);
    create(createPrinterDto: CreatePrinterDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        active: boolean;
        branchId: string;
        printerGroupId: string | null;
        type: import("generated/prisma").$Enums.PrinterType;
        connectionType: string;
        ipAddress: string | null;
        port: number | null;
    }>;
    findAll(branchId?: string, printerGroupId?: string, type?: PrinterType, active?: boolean): Promise<({
        branch: {
            name: string;
            id: string;
        };
        printerGroup: {
            name: string;
            id: string;
        } | null;
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        active: boolean;
        branchId: string;
        printerGroupId: string | null;
        type: import("generated/prisma").$Enums.PrinterType;
        connectionType: string;
        ipAddress: string | null;
        port: number | null;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        printerGroup: {
            name: string;
            id: string;
        } | null;
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        active: boolean;
        branchId: string;
        printerGroupId: string | null;
        type: import("generated/prisma").$Enums.PrinterType;
        connectionType: string;
        ipAddress: string | null;
        port: number | null;
    }>;
    update(id: string, updatePrinterDto: UpdatePrinterDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        active: boolean;
        branchId: string;
        printerGroupId: string | null;
        type: import("generated/prisma").$Enums.PrinterType;
        connectionType: string;
        ipAddress: string | null;
        port: number | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        active: boolean;
        branchId: string;
        printerGroupId: string | null;
        type: import("generated/prisma").$Enums.PrinterType;
        connectionType: string;
        ipAddress: string | null;
        port: number | null;
    }>;
}
export {};
