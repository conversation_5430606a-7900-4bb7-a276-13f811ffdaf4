"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrinterController = void 0;
const common_1 = require("@nestjs/common");
const printer_service_1 = require("./printer.service");
const create_printer_dto_1 = require("./dto/create-printer.dto");
const update_printer_dto_1 = require("./dto/update-printer.dto");
var PrinterType;
(function (PrinterType) {
    PrinterType["THERMAL"] = "THERMAL";
    PrinterType["DOT_MATRIX"] = "DOT_MATRIX";
    PrinterType["A4"] = "A4";
})(PrinterType || (PrinterType = {}));
let PrinterController = class PrinterController {
    printerService;
    constructor(printerService) {
        this.printerService = printerService;
    }
    create(createPrinterDto) {
        return this.printerService.createPrinter(createPrinterDto);
    }
    findAll(branchId, printerGroupId, type, active) {
        return this.printerService.findAllPrinters(branchId, printerGroupId, type, active);
    }
    findOne(id) {
        return this.printerService.findOnePrinter(id);
    }
    update(id, updatePrinterDto) {
        return this.printerService.updatePrinter(id, updatePrinterDto);
    }
    remove(id) {
        return this.printerService.removePrinter(id);
    }
};
exports.PrinterController = PrinterController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_printer_dto_1.CreatePrinterDto]),
    __metadata("design:returntype", void 0)
], PrinterController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('branchId')),
    __param(1, (0, common_1.Query)('printerGroupId')),
    __param(2, (0, common_1.Query)('type')),
    __param(3, (0, common_1.Query)('active', new common_1.ParseBoolPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Boolean]),
    __metadata("design:returntype", void 0)
], PrinterController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PrinterController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_printer_dto_1.UpdatePrinterDto]),
    __metadata("design:returntype", void 0)
], PrinterController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PrinterController.prototype, "remove", null);
exports.PrinterController = PrinterController = __decorate([
    (0, common_1.Controller)('printer'),
    __metadata("design:paramtypes", [printer_service_1.PrinterService])
], PrinterController);
//# sourceMappingURL=printer.controller.js.map