export declare class CreateDailyReportDto {
    branchId: string;
    reportDate: Date;
    createdBy: string;
    totalOrders?: number;
    totalItems?: number;
    totalCustomers?: number;
    averageTicket?: number;
    grossSales?: number;
    totalDiscount?: number;
    totalServiceCharge?: number;
    netSales?: number;
    totalTax?: number;
    totalSales?: number;
    cashSales?: number;
    creditCardSales?: number;
    debitCardSales?: number;
    mealCardSales?: number;
    otherSales?: number;
    totalReturns?: number;
    totalCancellations?: number;
    openingBalance?: number;
    totalCashIn?: number;
    totalCashOut?: number;
    expectedBalance?: number;
    actualBalance?: number;
    difference?: number;
    taxBreakdown?: any;
    categoryBreakdown?: any;
    hourlyBreakdown?: any;
    zReportNo?: string;
    fiscalId?: string;
    approvedBy?: string;
}
