import { PrismaService } from '../prisma/prisma.service';
import { CreatePriceOverrideDto } from './dto/create-price-override.dto';
import { UpdatePriceOverrideDto } from './dto/update-price-override.dto';
export declare class PriceOverrideService {
    private prisma;
    constructor(prisma: PrismaService);
    createPriceOverride(data: CreatePriceOverrideDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
    findAllPriceOverrides(branchId?: string, productId?: string, variantId?: string, activeOnly?: boolean, date?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
        product: {
            name: string;
            id: string;
            code: string;
        };
        variant: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    })[]>;
    findOnePriceOverride(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        product: {
            name: string;
            id: string;
            code: string;
        };
        variant: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
    updatePriceOverride(id: string, data: UpdatePriceOverrideDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
    removePriceOverride(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        productId: string;
        variantId: string | null;
        startDate: Date;
        endDate: Date | null;
        createdBy: string;
        reason: string | null;
        overridePrice: import("generated/prisma/runtime/library").Decimal;
    }>;
}
