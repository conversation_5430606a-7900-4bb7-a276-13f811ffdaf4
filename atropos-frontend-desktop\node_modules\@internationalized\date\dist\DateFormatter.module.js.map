{"mappings": "AAAA;;;;;;;;;;CAUC,GAED,IAAI,uCAAiB,IAAI;AAOlB,MAAM;IAUX,qGAAqG,GACrG,OAAO,KAAW,EAAU;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B;IAEA,4FAA4F,GAC5F,cAAc,KAAW,EAA6B;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACtC;IAEA,sCAAsC,GACtC,YAAY,KAAW,EAAE,GAAS,EAAU;QAC1C,aAAa;QACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK,YACxC,aAAa;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO;QAG3C,IAAI,MAAM,OACR,MAAM,IAAI,WAAW;QAGvB,wCAAwC;QACxC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,UAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM;IAC1E;IAEA,+CAA+C,GAC/C,mBAAmB,KAAW,EAAE,GAAS,EAAyB;QAChE,aAAa;QACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,YAC/C,aAAa;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO;QAGlD,IAAI,MAAM,OACR,MAAM,IAAI,WAAW;QAGvB,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;QAC9C,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;QAC5C,OAAO;eACF,WAAW,GAAG,CAAC,CAAA,IAAM,CAAA;oBAAC,GAAG,CAAC;oBAAE,QAAQ;gBAAY,CAAA;YACnD;gBAAC,MAAM;gBAAW,OAAO;gBAAO,QAAQ;YAAQ;eAC7C,SAAS,GAAG,CAAC,CAAA,IAAM,CAAA;oBAAC,GAAG,CAAC;oBAAE,QAAQ;gBAAU,CAAA;SAChD;IACH;IAEA,2FAA2F,GAC3F,kBAAsD;QACpD,IAAI,kBAAkB,IAAI,CAAC,SAAS,CAAC,eAAe;QACpD,IAAI,mDAA6B;YAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EACzB,IAAI,CAAC,iBAAiB,GAAG,2CAAqB,gBAAgB,MAAM,EAAE,IAAI,CAAC,OAAO;YAEpF,gBAAgB,SAAS,GAAG,IAAI,CAAC,iBAAiB;YAClD,gBAAgB,MAAM,GAAG,IAAI,CAAC,iBAAiB,KAAK,SAAS,IAAI,CAAC,iBAAiB,KAAK;QAC1F;QAEA,uEAAuE;QACvE,iDAAiD;QACjD,IAAI,gBAAgB,QAAQ,KAAK,uBAC/B,gBAAgB,QAAQ,GAAG;QAG7B,OAAO;IACT;IAtEA,YAAY,MAAc,EAAE,UAAsC,CAAC,CAAC,CAAE;QACpE,IAAI,CAAC,SAAS,GAAG,6CAAuB,QAAQ;QAChD,IAAI,CAAC,OAAO,GAAG;IACjB;AAoEF;AAEA,iGAAiG;AACjG,uHAAuH;AACvH,iIAAiI;AACjI,uCAAuC;AACvC,yHAAyH;AACzH,yHAAyH;AACzH,oEAAoE;AACpE,iDAAiD;AACjD,qDAAqD;AAErD,yIAAyI;AACzI,MAAM,0CAAoB;IACxB,MAAM;QACJ,yEAAyE;QACzE,IAAI;IACN;IACA,OAAO;IAEP;AACF;AAEA,SAAS,6CAAuB,MAAc,EAAE,UAAsC,CAAC,CAAC;IACtF,0FAA0F;IAC1F,uGAAuG;IACvG,IAAI,OAAO,QAAQ,MAAM,KAAK,aAAa,gDAA0B;QACnE,UAAU;YAAC,GAAG,OAAO;QAAA;QACrB,IAAI,OAAO,uCAAiB,CAAC,OAAO,QAAQ,MAAM,EAAE,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1E,IAAI,mBAAmB,QAAQ,MAAM,GAAG,QAAQ;QAChD,QAAQ,SAAS,GAAG,iBAAA,kBAAA,OAAQ;QAC5B,OAAO,QAAQ,MAAM;IACvB;IAEA,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,qCAAe,GAAG,CAAC,WACrB,OAAO,qCAAe,GAAG,CAAC;IAG5B,IAAI,kBAAkB,IAAI,KAAK,cAAc,CAAC,QAAQ;IACtD,qCAAe,GAAG,CAAC,UAAU;IAC7B,OAAO;AACT;AAEA,IAAI,gDAA0C;AAC9C,SAAS;IACP,IAAI,iDAA2B,MAC7B,gDAA0B,IAAI,KAAK,cAAc,CAAC,SAAS;QACzD,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK,MAAM,GAAG,GAAG,QAAQ;IAGzC,OAAO;AACT;AAEA,IAAI,mDAA6C;AACjD,SAAS;IACP,IAAI,oDAA8B,MAChC,mDAA6B,IAAI,KAAK,cAAc,CAAC,MAAM;QACzD,MAAM;QACN,QAAQ;IACV,GAAG,eAAe,GAAG,SAAS,KAAK;IAGrC,OAAO;AACT;AAEA,SAAS,2CAAqB,MAAc,EAAE,OAAmC;IAC/E,IAAI,CAAC,QAAQ,SAAS,IAAI,CAAC,QAAQ,IAAI,EACrC,OAAO;IAGT,gFAAgF;IAChF,6FAA6F;IAC7F,SAAS,OAAO,OAAO,CAAC,0BAA0B;IAClD,UAAU,AAAC,CAAA,OAAO,QAAQ,CAAC,SAAS,KAAK,IAAG,IAAK;IACjD,IAAI,YAAY,6CAAuB,QAAQ;QAC7C,GAAG,OAAO;QACV,UAAU,UAAU,qBAAqB;IAC3C;IAEA,IAAI,MAAM,SAAS,UAAU,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAS,KAAK,EAAE;IACzG,IAAI,MAAM,SAAS,UAAU,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,GAAG,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAS,KAAK,EAAE;IAE1G,IAAI,QAAQ,KAAK,QAAQ,IACvB,OAAO;IAGT,IAAI,QAAQ,MAAM,QAAQ,IACxB,OAAO;IAGT,IAAI,QAAQ,KAAK,QAAQ,IACvB,OAAO;IAGT,IAAI,QAAQ,MAAM,QAAQ,IACxB,OAAO;IAGT,MAAM,IAAI,MAAM;AAClB", "sources": ["packages/@internationalized/date/src/DateFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.DateTimeFormat>();\n\ninterface DateRangeFormatPart extends Intl.DateTimeFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/** A wrapper around Intl.DateTimeFormat that fixes various browser bugs, and polyfills new features. */\nexport class DateFormatter implements Intl.DateTimeFormat {\n  private formatter: Intl.DateTimeFormat;\n  private options: Intl.DateTimeFormatOptions;\n  private resolvedHourCycle: Intl.DateTimeFormatOptions['hourCycle'];\n\n  constructor(locale: string, options: Intl.DateTimeFormatOptions = {}) {\n    this.formatter = getCachedDateFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a date as a string according to the locale and format options passed to the constructor. */\n  format(value: Date): string {\n    return this.formatter.format(value);\n  }\n\n  /** Formats a date to an array of parts such as separators, numbers, punctuation, and more. */\n  formatToParts(value: Date): Intl.DateTimeFormatPart[] {\n    return this.formatter.formatToParts(value);\n  }\n\n  /** Formats a date range as a string. */\n  formatRange(start: Date, end: Date): string {\n    // @ts-ignore\n    if (typeof this.formatter.formatRange === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.formatter.format(start)} – ${this.formatter.format(end)}`;\n  }\n\n  /** Formats a date range as an array of parts. */\n  formatRangeToParts(start: Date, end: Date): DateRangeFormatPart[] {\n    // @ts-ignore\n    if (typeof this.formatter.formatRangeToParts === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.formatter.formatToParts(start);\n    let endParts = this.formatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as DateRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as DateRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedDateTimeFormatOptions {\n    let resolvedOptions = this.formatter.resolvedOptions();\n    if (hasBuggyResolvedHourCycle()) {\n      if (!this.resolvedHourCycle) {\n        this.resolvedHourCycle = getResolvedHourCycle(resolvedOptions.locale, this.options);\n      }\n      resolvedOptions.hourCycle = this.resolvedHourCycle;\n      resolvedOptions.hour12 = this.resolvedHourCycle === 'h11' || this.resolvedHourCycle === 'h12';\n    }\n\n    // Safari uses a different name for the Ethiopic (Amete Alem) calendar.\n    // https://bugs.webkit.org/show_bug.cgi?id=241564\n    if (resolvedOptions.calendar === 'ethiopic-amete-alem') {\n      resolvedOptions.calendar = 'ethioaa';\n    }\n\n    return resolvedOptions;\n  }\n}\n\n// There are multiple bugs involving the hour12 and hourCycle options in various browser engines.\n//   - Chrome [1] (and the ECMA 402 spec [2]) resolve hour12: false in English and other locales to h24 (24:00 - 23:59)\n//     rather than h23 (00:00 - 23:59). Same can happen with hour12: true in French, which Chrome resolves to h11 (00:00 - 11:59)\n//     rather than h12 (12:00 - 11:59).\n//   - WebKit returns an incorrect hourCycle resolved option in the French locale due to incorrect parsing of 'h' literal\n//     in the resolved pattern. It also formats incorrectly when specifying the hourCycle option for the same reason. [3]\n// [1] https://bugs.chromium.org/p/chromium/issues/detail?id=1045791\n// [2] https://github.com/tc39/ecma402/issues/402\n// [3] https://bugs.webkit.org/show_bug.cgi?id=229313\n\n// https://github.com/unicode-org/cldr/blob/018b55eff7ceb389c7e3fc44e2f657eae3b10b38/common/supplemental/supplementalData.xml#L4774-L4802\nconst hour12Preferences = {\n  true: {\n    // Only Japanese uses the h11 style for 12 hour time. All others use h12.\n    ja: 'h11'\n  },\n  false: {\n    // All locales use h23 for 24 hour time. None use h24.\n  }\n};\n\nfunction getCachedDateFormatter(locale: string, options: Intl.DateTimeFormatOptions = {}): Intl.DateTimeFormat {\n  // Work around buggy hour12 behavior in Chrome / ECMA 402 spec by using hourCycle instead.\n  // Only apply the workaround if the issue is detected, because the hourCycle option is buggy in Safari.\n  if (typeof options.hour12 === 'boolean' && hasBuggyHour12Behavior()) {\n    options = {...options};\n    let pref = hour12Preferences[String(options.hour12)][locale.split('-')[0]];\n    let defaultHourCycle = options.hour12 ? 'h12' : 'h23';\n    options.hourCycle = pref ?? defaultHourCycle;\n    delete options.hour12;\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.DateTimeFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\nlet _hasBuggyHour12Behavior: boolean | null = null;\nfunction hasBuggyHour12Behavior() {\n  if (_hasBuggyHour12Behavior == null) {\n    _hasBuggyHour12Behavior = new Intl.DateTimeFormat('en-US', {\n      hour: 'numeric',\n      hour12: false\n    }).format(new Date(2020, 2, 3, 0)) === '24';\n  }\n\n  return _hasBuggyHour12Behavior;\n}\n\nlet _hasBuggyResolvedHourCycle: boolean | null = null;\nfunction hasBuggyResolvedHourCycle() {\n  if (_hasBuggyResolvedHourCycle == null) {\n    _hasBuggyResolvedHourCycle = new Intl.DateTimeFormat('fr', {\n      hour: 'numeric',\n      hour12: false\n    }).resolvedOptions().hourCycle === 'h12';\n  }\n\n  return _hasBuggyResolvedHourCycle;\n}\n\nfunction getResolvedHourCycle(locale: string, options: Intl.DateTimeFormatOptions) {\n  if (!options.timeStyle && !options.hour) {\n    return undefined;\n  }\n\n  // Work around buggy results in resolved hourCycle and hour12 options in WebKit.\n  // Format the minimum possible hour and maximum possible hour in a day and parse the results.\n  locale = locale.replace(/(-u-)?-nu-[a-zA-Z0-9]+/, '');\n  locale += (locale.includes('-u-') ? '' : '-u') + '-nu-latn';\n  let formatter = getCachedDateFormatter(locale, {\n    ...options,\n    timeZone: undefined // use local timezone\n  });\n\n  let min = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 0)).find(p => p.type === 'hour')!.value, 10);\n  let max = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 23)).find(p => p.type === 'hour')!.value, 10);\n\n  if (min === 0 && max === 23) {\n    return 'h23';\n  }\n\n  if (min === 24 && max === 23) {\n    return 'h24';\n  }\n\n  if (min === 0 && max === 11) {\n    return 'h11';\n  }\n\n  if (min === 12 && max === 11) {\n    return 'h12';\n  }\n\n  throw new Error('Unexpected hour cycle result');\n}\n"], "names": [], "version": 3, "file": "DateFormatter.module.js.map"}