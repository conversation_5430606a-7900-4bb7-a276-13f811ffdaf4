import { LoyaltyCardService } from './loyalty-card.service';
import { CreateLoyaltyCardDto } from './dto/create-loyalty-card.dto';
import { UpdateLoyaltyCardDto } from './dto/update-loyalty-card.dto';
export declare class LoyaltyCardController {
    private readonly loyaltyCardService;
    constructor(loyaltyCardService: LoyaltyCardService);
    create(createLoyaltyCardDto: CreateLoyaltyCardDto): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    findAll(customerId?: string, cardNumber?: string, cardType?: string): Promise<{
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        };
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }[]>;
    findOne(id: string): Promise<{
        customer: {
            phone: string;
            id: string;
            firstName: string | null;
            lastName: string | null;
        };
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    update(id: string, updateLoyaltyCardDto: UpdateLoyaltyCardDto): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        active: boolean;
        blocked: boolean;
        blockReason: string | null;
    }>;
    addPoints(id: string, body: {
        points: number;
    }): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    spendPoints(id: string, body: {
        points: number;
    }): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    addBalance(id: string, body: {
        amount: number;
    }): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    spendBalance(id: string, body: {
        amount: number;
    }): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    blockCard(id: string, body: {
        reason: string;
    }): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    unblockCard(id: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    activateCard(id: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
    deactivateCard(id: string): Promise<{
        id: string;
        active: boolean;
        customerId: string;
        discountRate: import("generated/prisma/runtime/library").Decimal;
        cardNumber: string;
        cardType: import("generated/prisma").$Enums.LoyaltyCardType;
        points: number;
        totalEarnedPoints: number;
        totalSpentPoints: number;
        balance: import("generated/prisma/runtime/library").Decimal;
        totalLoaded: import("generated/prisma/runtime/library").Decimal;
        issuedAt: Date;
        activatedAt: Date | null;
        expiresAt: Date | null;
        blocked: boolean;
        blockReason: string | null;
        lastUsedAt: Date | null;
    }>;
}
