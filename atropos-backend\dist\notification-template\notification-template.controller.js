"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationTemplateController = void 0;
const common_1 = require("@nestjs/common");
const notification_template_service_1 = require("./notification-template.service");
const create_notification_template_dto_1 = require("./dto/create-notification-template.dto");
const update_notification_template_dto_1 = require("./dto/update-notification-template.dto");
const prisma_1 = require("../../generated/prisma");
const parse_optional_enum_pipe_1 = require("../common/pipes/parse-optional-enum.pipe");
let NotificationTemplateController = class NotificationTemplateController {
    notificationTemplateService;
    constructor(notificationTemplateService) {
        this.notificationTemplateService = notificationTemplateService;
    }
    create(createNotificationTemplateDto) {
        return this.notificationTemplateService.createNotificationTemplate(createNotificationTemplateDto);
    }
    findAll(companyId, channel, active) {
        return this.notificationTemplateService.findAllNotificationTemplates(companyId, channel, active);
    }
    findOne(id) {
        return this.notificationTemplateService.findOneNotificationTemplate(id);
    }
    update(id, updateNotificationTemplateDto) {
        return this.notificationTemplateService.updateNotificationTemplate(id, updateNotificationTemplateDto);
    }
    remove(id) {
        return this.notificationTemplateService.removeNotificationTemplate(id);
    }
};
exports.NotificationTemplateController = NotificationTemplateController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_notification_template_dto_1.CreateNotificationTemplateDto]),
    __metadata("design:returntype", void 0)
], NotificationTemplateController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('companyId')),
    __param(1, (0, common_1.Query)('channel', new parse_optional_enum_pipe_1.ParseOptionalEnumPipe(prisma_1.NotificationChannel))),
    __param(2, (0, common_1.Query)('active', new common_1.ParseBoolPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Boolean]),
    __metadata("design:returntype", void 0)
], NotificationTemplateController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationTemplateController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_notification_template_dto_1.UpdateNotificationTemplateDto]),
    __metadata("design:returntype", void 0)
], NotificationTemplateController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationTemplateController.prototype, "remove", null);
exports.NotificationTemplateController = NotificationTemplateController = __decorate([
    (0, common_1.Controller)('notification-template'),
    __metadata("design:paramtypes", [notification_template_service_1.NotificationTemplateService])
], NotificationTemplateController);
//# sourceMappingURL=notification-template.controller.js.map