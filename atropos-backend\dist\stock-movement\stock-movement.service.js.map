{"version": 3, "file": "stock-movement.service.js", "sourceRoot": "", "sources": ["../../src/stock-movement/stock-movement.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,2CAA2H;AAC3H,6DAAyD;AAGzD,mDAA2D;AAGpD,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IACX;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,mBAAmB,CAAC,IAA4B;QAEpD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAC5G,IAAI,CAAC,YAAY,EAAE,CAAC;YAAC,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,IAAI,CAAC,QAAQ,cAAc,CAAC,CAAC;QAAC,CAAC;QACnG,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAC9G,IAAI,CAAC,eAAe,EAAE,CAAC;YAAC,MAAM,IAAI,0BAAiB,CAAC,6BAA6B,IAAI,CAAC,SAAS,cAAc,CAAC,CAAC;QAAC,CAAC;QACjH,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAChH,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAAC,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,IAAI,CAAC,UAAU,cAAc,CAAC,CAAC;YAAC,CAAC;QACxH,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3F,MAAM,IAAI,4BAAmB,CAAC,qEAAqE,CAAC,CAAC;QACvG,CAAC;QAED,IAAI,mBAAwB,CAAC;QAC7B,IAAI,iBAA8C,CAAC;QAEnD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACzG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAAC,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,IAAI,CAAC,SAAS,cAAc,CAAC,CAAC;YAAC,CAAC;YAEhG,mBAAmB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAC3H,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,mBAAmB,GAAG,OAAO,CAAC;gBAC9B,iBAAiB,GAAG,SAAS,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,iBAAiB,GAAG,eAAe,CAAC;YACtC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAC3H,IAAI,CAAC,aAAa,EAAE,CAAC;gBAAC,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,IAAI,CAAC,eAAe,cAAc,CAAC,CAAC;YAAC,CAAC;YACnH,mBAAmB,GAAG,aAAa,CAAC;YACpC,iBAAiB,GAAG,eAAe,CAAC;QACtC,CAAC;QAGD,IAAI,mBAAmB,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YAC3C,MAAM,IAAI,4BAAmB,CAAC,sCAAsC,mBAAmB,CAAC,IAAI,0BAA0B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACtI,CAAC;QAGD,IAAI,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG;YACpB,0BAAiB,CAAC,IAAI,EAAE,0BAAiB,CAAC,UAAU,EAAE,0BAAiB,CAAC,KAAK;YAC7E,0BAAiB,CAAC,MAAM,EAAE,0BAAiB,CAAC,KAAK,EAAE,0BAAiB,CAAC,YAAY;YACjF,0BAAiB,CAAC,WAAW,EAAE,0BAAiB,CAAC,MAAM,EAAE,0BAAiB,CAAC,IAAI;YAC/E,0BAAiB,CAAC,oBAAoB;SACvC,CAAC;QACF,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAW,CAAC,EAAE,CAAC;YAC7C,gBAAgB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAClJ,MAAM,eAAe,GAAG,aAAa,GAAG,gBAAgB,CAAC;QAEzD,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,4BAAmB,CAAC,wDAAwD,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,yBAAyB,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACrM,CAAC;QAGD,IAAI,YAAY,GAAG,mBAAmB,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QACpE,IAAI,cAAc,GAAG,YAAY,CAAC;QAClC,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,IAAI,IAAI,CAAC,IAAI,KAAK,0BAAiB,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,IAAI,4BAAmB,CAAC,iEAAiE,CAAC,CAAC;YACnG,CAAC;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnD,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAEhD,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACrC,cAAc,GAAG,CAAC,CAAC,aAAa,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtG,CAAC;iBAAM,CAAC;gBACL,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;YAClC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,YAAY,CAAC;YAEtD,cAAc,GAAG,YAAY,CAAC;QAChC,CAAC;QAGD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAC7D,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,QAAQ,EAAE,gBAAgB;gBAC1B,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACxF,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC3C,YAAY,EAAE,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACjD,cAAc,EAAE,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACrD,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACnD,YAAY,EAAE,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpD,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,eAAe,EAAE,IAAI,CAAC,eAAe;aACtC;SACF,CAAC,CAAC;QAGH,MAAM,UAAU,GAAQ;YACtB,YAAY,EAAE,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpD,cAAc,EAAE,UAAU,CAAC,CAAC,eAAe,GAAG,CAAC,mBAAmB,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/G,WAAW,EAAE,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACnD,CAAC;QAEF,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QAStC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBACrC,KAAK,EAAE,EAAE,EAAE,EAAE,mBAAmB,CAAC,EAAE,EAAE;gBACrC,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,QAAiB,EACjB,SAAkB,EAClB,eAAwB,EACxB,IAAwB,EACxB,SAAgB,EAChB,OAAc;QAEd,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACxC,KAAK,EAAE;gBACL,QAAQ,EAAE,QAAQ,IAAI,SAAS;gBAC/B,SAAS,EAAE,SAAS,IAAI,SAAS;gBACjC,eAAe,EAAE,eAAe,IAAI,SAAS;gBAC7C,IAAI,EAAE,IAAI,IAAI,SAAS;gBACvB,SAAS,EAAE;oBACT,GAAG,EAAE,SAAS,IAAI,SAAS;oBAC3B,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBACnF;aACF;YACD,OAAO,EAAE;gBACP,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBAC5C,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBACzD,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;aAChE;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,EAAU;QACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YAC1D,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBAC5C,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBACzD,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;aAChE;SACF,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;QAC3E,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,EAAU,EAAE,IAA4B;QAKhE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAE7D,IAAK,IAAY,CAAC,QAAQ,KAAK,SAAS,IAAK,IAAY,CAAC,IAAI,KAAK,SAAS,IAAK,IAAY,CAAC,IAAI,KAAK,SAAS,IAAK,IAAY,CAAC,QAAQ,KAAK,SAAS;YACnJ,IAAY,CAAC,SAAS,KAAK,SAAS,IAAK,IAAY,CAAC,eAAe,KAAK,SAAS,IAAK,IAAY,CAAC,QAAQ,KAAK,SAAS,IAAK,IAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACxK,MAAM,IAAI,2BAAkB,CAAC,uLAAuL,CAAC,CAAC;QAC1N,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;YAC3E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,EAAU;QAMlC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBACtD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAKH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;YAC3E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA3NY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,oBAAoB,CA2NhC"}