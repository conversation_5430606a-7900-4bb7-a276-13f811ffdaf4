"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyCardController = void 0;
const common_1 = require("@nestjs/common");
const loyalty_card_service_1 = require("./loyalty-card.service");
const create_loyalty_card_dto_1 = require("./dto/create-loyalty-card.dto");
const update_loyalty_card_dto_1 = require("./dto/update-loyalty-card.dto");
let LoyaltyCardController = class LoyaltyCardController {
    loyaltyCardService;
    constructor(loyaltyCardService) {
        this.loyaltyCardService = loyaltyCardService;
    }
    create(createLoyaltyCardDto) {
        return this.loyaltyCardService.createLoyaltyCard(createLoyaltyCardDto);
    }
    findAll(customerId, cardNumber, cardType) {
        return this.loyaltyCardService.findAllLoyaltyCards(customerId, cardNumber, cardType);
    }
    findOne(id) {
        return this.loyaltyCardService.findOneLoyaltyCard(id);
    }
    update(id, updateLoyaltyCardDto) {
        return this.loyaltyCardService.updateLoyaltyCard(id, updateLoyaltyCardDto);
    }
    remove(id) {
        return this.loyaltyCardService.removeLoyaltyCard(id);
    }
    addPoints(id, body) {
        return this.loyaltyCardService.addPoints(id, body.points);
    }
    spendPoints(id, body) {
        return this.loyaltyCardService.spendPoints(id, body.points);
    }
    addBalance(id, body) {
        return this.loyaltyCardService.addBalance(id, body.amount);
    }
    spendBalance(id, body) {
        return this.loyaltyCardService.spendBalance(id, body.amount);
    }
    blockCard(id, body) {
        return this.loyaltyCardService.blockCard(id, body.reason);
    }
    unblockCard(id) {
        return this.loyaltyCardService.unblockCard(id);
    }
    activateCard(id) {
        return this.loyaltyCardService.activateCard(id);
    }
    deactivateCard(id) {
        return this.loyaltyCardService.deactivateCard(id);
    }
};
exports.LoyaltyCardController = LoyaltyCardController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_loyalty_card_dto_1.CreateLoyaltyCardDto]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('customerId')),
    __param(1, (0, common_1.Query)('cardNumber')),
    __param(2, (0, common_1.Query)('cardType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_loyalty_card_dto_1.UpdateLoyaltyCardDto]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/add-points'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "addPoints", null);
__decorate([
    (0, common_1.Post)(':id/spend-points'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "spendPoints", null);
__decorate([
    (0, common_1.Post)(':id/add-balance'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "addBalance", null);
__decorate([
    (0, common_1.Post)(':id/spend-balance'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "spendBalance", null);
__decorate([
    (0, common_1.Post)(':id/block'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "blockCard", null);
__decorate([
    (0, common_1.Post)(':id/unblock'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "unblockCard", null);
__decorate([
    (0, common_1.Post)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "activateCard", null);
__decorate([
    (0, common_1.Post)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyCardController.prototype, "deactivateCard", null);
exports.LoyaltyCardController = LoyaltyCardController = __decorate([
    (0, common_1.Controller)('loyalty-card'),
    __metadata("design:paramtypes", [loyalty_card_service_1.LoyaltyCardService])
], LoyaltyCardController);
//# sourceMappingURL=loyalty-card.controller.js.map