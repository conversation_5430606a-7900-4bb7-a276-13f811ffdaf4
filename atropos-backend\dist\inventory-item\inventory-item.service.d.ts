import { PrismaService } from '../prisma/prisma.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';
export declare class InventoryItemService {
    private prisma;
    constructor(prisma: PrismaService);
    createInventoryItem(data: CreateInventoryItemDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
    findAllInventoryItems(productId?: string, code?: string, supplier?: string): Promise<({
        product: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    })[]>;
    findOneInventoryItem(id: string): Promise<{
        product: {
            name: string;
            id: string;
            code: string;
        } | null;
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
    updateInventoryItem(id: string, data: UpdateInventoryItemDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
    removeInventoryItem(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        code: string;
        active: boolean;
        version: number;
        barcode: string | null;
        unit: import("generated/prisma").$Enums.ProductUnit;
        productId: string | null;
        currentStock: import("generated/prisma/runtime/library").Decimal;
        reservedStock: import("generated/prisma/runtime/library").Decimal;
        availableStock: import("generated/prisma/runtime/library").Decimal;
        criticalLevel: import("generated/prisma/runtime/library").Decimal | null;
        optimalLevel: import("generated/prisma/runtime/library").Decimal | null;
        lastCost: import("generated/prisma/runtime/library").Decimal | null;
        averageCost: import("generated/prisma/runtime/library").Decimal | null;
        supplier: string | null;
        supplierCode: string | null;
        location: string | null;
        expiryDate: Date | null;
    }>;
}
