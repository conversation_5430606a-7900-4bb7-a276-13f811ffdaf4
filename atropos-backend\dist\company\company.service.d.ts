import { PrismaService } from '../prisma/prisma.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
export declare class CompanyService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    createCompany(data: CreateCompanyDto): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    findAllCompanies(): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }[]>;
    findOneCompany(id: string): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateCompany(id: string, data: UpdateCompanyDto): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    removeCompany(id: string): Promise<{
        name: string;
        taxNumber: string;
        taxOffice: string;
        address: string;
        phone: string;
        email: string;
        logo: string | null;
        website: string | null;
        eArchiveUsername: string | null;
        eArchivePassword: string | null;
        eInvoiceUsername: string | null;
        eInvoicePassword: string | null;
        smsProvider: string | null;
        smsApiKey: string | null;
        smsApiSecret: string | null;
        smsSenderName: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
}
