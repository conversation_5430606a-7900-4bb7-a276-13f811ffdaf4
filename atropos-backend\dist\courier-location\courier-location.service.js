"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourierLocationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let CourierLocationService = class CourierLocationService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createCourierLocation(data) {
        const courier = await this.prisma.user.findUnique({
            where: { id: data.courierId, deletedAt: null },
        });
        if (!courier) {
            throw new common_1.NotFoundException(`Courier with ID "${data.courierId}" not found.`);
        }
        if (courier.role !== prisma_1.UserRole.COURIER && courier.role !== prisma_1.UserRole.ADMIN) {
            throw new common_1.BadRequestException(`User with ID "${data.courierId}" is not a COURIER.`);
        }
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        const timestamp = data.timestamp ? new Date(data.timestamp) : new Date();
        const expiresAt = new Date(timestamp.getTime() + (7 * 24 * 60 * 60 * 1000));
        return this.prisma.courierLocation.create({
            data: {
                ...data,
                timestamp: timestamp,
                expiresAt: expiresAt,
            },
        });
    }
    async findAllCourierLocations(courierId, branchId, startDate, endDate) {
        return this.prisma.courierLocation.findMany({
            where: {
                courierId: courierId || undefined,
                branchId: branchId || undefined,
                timestamp: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
                expiresAt: {
                    gte: new Date()
                }
            },
            include: {
                courier: { select: { id: true, firstName: true, lastName: true, employeeCode: true } },
                branch: { select: { id: true, name: true } },
            },
            orderBy: { timestamp: 'desc' },
        });
    }
    async findOneCourierLocation(id) {
        const location = await this.prisma.courierLocation.findUnique({
            where: { id, expiresAt: { gte: new Date() } },
            include: {
                courier: { select: { id: true, firstName: true, lastName: true, employeeCode: true } },
                branch: { select: { id: true, name: true } },
            },
        });
        if (!location) {
            throw new common_1.NotFoundException(`Courier location with ID "${id}" not found or expired.`);
        }
        return location;
    }
    async updateCourierLocation(id, data) {
        const existingLocation = await this.findOneCourierLocation(id);
        if (data.courierId !== undefined || data.branchId !== undefined ||
            data.latitude !== undefined || data.longitude !== undefined) {
            throw new common_1.ForbiddenException('Cannot update courierId, branchId, latitude, or longitude of a courier location log. Create a new log instead.');
        }
        try {
            return await this.prisma.courierLocation.update({
                where: { id },
                data: {
                    timestamp: data.timestamp ? new Date(data.timestamp) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Courier location with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeCourierLocation(id) {
        try {
            return await this.prisma.courierLocation.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Courier location with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.CourierLocationService = CourierLocationService;
exports.CourierLocationService = CourierLocationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CourierLocationService);
//# sourceMappingURL=courier-location.service.js.map