"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let TaxService = class TaxService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createTax(data) {
        const existingTax = await this.prisma.tax.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingTax) {
            throw new common_1.ConflictException(`Tax with code "${data.code}" already exists for this company.`);
        }
        return this.prisma.tax.create({ data: {
                ...data,
                rate: parseFloat(data.rate.toFixed(2)),
            } });
    }
    async findAllTaxes(companyId) {
        return this.prisma.tax.findMany({
            where: { companyId: companyId || undefined, deletedAt: null },
            include: { company: { select: { id: true, name: true } } },
        });
    }
    async findOneTax(id) {
        const tax = await this.prisma.tax.findUnique({
            where: { id, deletedAt: null },
            include: { company: { select: { id: true, name: true } } },
        });
        if (!tax) {
            throw new common_1.NotFoundException(`Tax with ID "${id}" not found.`);
        }
        return tax;
    }
    async updateTax(id, data) {
        if (data.code) {
            const currentTax = await this.findOneTax(id);
            const existingTax = await this.prisma.tax.findUnique({
                where: {
                    companyId_code: {
                        companyId: data.companyId || currentTax.companyId,
                        code: data.code,
                    },
                },
            });
            if (existingTax && existingTax.id !== id) {
                throw new common_1.ConflictException(`Tax with code "${data.code}" already exists for this company.`);
            }
        }
        try {
            return await this.prisma.tax.update({
                where: { id, deletedAt: null },
                data: {
                    ...data,
                    rate: data.rate !== undefined ? parseFloat(data.rate.toFixed(2)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Tax with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeTax(id) {
        const productsCount = await this.prisma.product.count({
            where: { taxId: id, deletedAt: null }
        });
        if (productsCount > 0) {
            throw new common_1.ConflictException(`Tax with ID "${id}" cannot be deleted because it has ${productsCount} active products.`);
        }
        try {
            return await this.prisma.tax.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Tax with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.TaxService = TaxService;
exports.TaxService = TaxService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TaxService);
//# sourceMappingURL=tax.service.js.map