import { LoyaltyCardType } from '../../../generated/prisma';
export declare class CreateLoyaltyCardDto {
    customerId: string;
    cardNumber: string;
    cardType?: LoyaltyCardType;
    points?: number;
    totalEarnedPoints?: number;
    totalSpentPoints?: number;
    balance?: number;
    totalLoaded?: number;
    discountRate?: number;
    pin?: string;
    issuedAt?: Date;
    activatedAt?: Date;
    expiresAt?: Date;
    blocked?: boolean;
    blockReason?: string;
    active?: boolean;
}
