import { PrismaService } from '../prisma/prisma.service';
import { CreateRecipeDto } from './dto/create-recipe.dto';
import { UpdateRecipeDto } from './dto/update-recipe.dto';
export declare class RecipeService {
    private prisma;
    constructor(prisma: PrismaService);
    createRecipe(data: CreateRecipeDto): Promise<{
        items: {
            id: string;
            unit: import("generated/prisma").$Enums.ProductUnit;
            quantity: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            wastagePercent: import("generated/prisma/runtime/library").Decimal;
            recipeId: string;
        }[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        version: number;
        preparationTime: number | null;
        productId: string;
        yield: import("generated/prisma/runtime/library").Decimal;
        preparationSteps: string | null;
    }>;
    findAllRecipes(productId?: string): Promise<({
        product: {
            name: string;
            id: string;
            code: string;
        };
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("generated/prisma").$Enums.ProductUnit;
            };
        } & {
            id: string;
            unit: import("generated/prisma").$Enums.ProductUnit;
            quantity: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            wastagePercent: import("generated/prisma/runtime/library").Decimal;
            recipeId: string;
        })[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        version: number;
        preparationTime: number | null;
        productId: string;
        yield: import("generated/prisma/runtime/library").Decimal;
        preparationSteps: string | null;
    })[]>;
    findOneRecipe(id: string): Promise<{
        product: {
            name: string;
            id: string;
            code: string;
        };
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("generated/prisma").$Enums.ProductUnit;
            };
        } & {
            id: string;
            unit: import("generated/prisma").$Enums.ProductUnit;
            quantity: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            wastagePercent: import("generated/prisma/runtime/library").Decimal;
            recipeId: string;
        })[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        version: number;
        preparationTime: number | null;
        productId: string;
        yield: import("generated/prisma/runtime/library").Decimal;
        preparationSteps: string | null;
    }>;
    updateRecipe(id: string, data: UpdateRecipeDto): Promise<{
        product: {
            name: string;
            id: string;
            code: string;
        };
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("generated/prisma").$Enums.ProductUnit;
            };
        } & {
            id: string;
            unit: import("generated/prisma").$Enums.ProductUnit;
            quantity: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            wastagePercent: import("generated/prisma/runtime/library").Decimal;
            recipeId: string;
        })[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        version: number;
        preparationTime: number | null;
        productId: string;
        yield: import("generated/prisma/runtime/library").Decimal;
        preparationSteps: string | null;
    }>;
    removeRecipe(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        version: number;
        preparationTime: number | null;
        productId: string;
        yield: import("generated/prisma/runtime/library").Decimal;
        preparationSteps: string | null;
    }>;
}
