import { NotificationTemplateService } from './notification-template.service';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationChannel } from '../../generated/prisma';
export declare class NotificationTemplateController {
    private readonly notificationTemplateService;
    constructor(notificationTemplateService: NotificationTemplateService);
    create(createNotificationTemplateDto: CreateNotificationTemplateDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    findAll(companyId?: string, channel?: NotificationChannel, active?: boolean): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    update(id: string, updateNotificationTemplateDto: UpdateNotificationTemplateDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import("../../generated/prisma").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
}
