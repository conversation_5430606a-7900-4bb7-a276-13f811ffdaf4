import { PrismaService } from '../prisma/prisma.service';
import { CreateStockCountDto } from './dto/create-stock-count.dto';
import { UpdateStockCountDto } from './dto/update-stock-count.dto';
import { StockCountStatus } from '../../generated/prisma';
import { StockMovementService } from '../stock-movement/stock-movement.service';
export declare class StockCountService {
    private prisma;
    private stockMovementService;
    constructor(prisma: PrismaService, stockMovementService: StockMovementService);
    private processStockCountItems;
    createStockCount(data: CreateStockCountDto): Promise<{
        items: {
            id: string;
            note: string | null;
            difference: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("generated/prisma/runtime/library").Decimal | null;
            countedQuantity: import("generated/prisma/runtime/library").Decimal;
            systemQuantity: import("generated/prisma/runtime/library").Decimal;
            totalDifference: import("generated/prisma/runtime/library").Decimal | null;
            stockCountId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
    findAllStockCounts(branchId?: string, countType?: any, status?: StockCountStatus, startDate?: Date, endDate?: Date): Promise<({} & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    })[]>;
    findOneStockCount(id: string): Promise<{
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("../../generated/prisma").$Enums.ProductUnit;
                currentStock: import("generated/prisma/runtime/library").Decimal;
                averageCost: import("generated/prisma/runtime/library").Decimal | null;
            };
        } & {
            id: string;
            note: string | null;
            difference: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("generated/prisma/runtime/library").Decimal | null;
            countedQuantity: import("generated/prisma/runtime/library").Decimal;
            systemQuantity: import("generated/prisma/runtime/library").Decimal;
            totalDifference: import("generated/prisma/runtime/library").Decimal | null;
            stockCountId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
    updateStockCount(id: string, data: UpdateStockCountDto): Promise<{
        items: ({
            inventoryItem: {
                name: string;
                id: string;
                unit: import("../../generated/prisma").$Enums.ProductUnit;
                currentStock: import("generated/prisma/runtime/library").Decimal;
                averageCost: import("generated/prisma/runtime/library").Decimal | null;
            };
        } & {
            id: string;
            note: string | null;
            difference: import("generated/prisma/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("generated/prisma/runtime/library").Decimal | null;
            countedQuantity: import("generated/prisma/runtime/library").Decimal;
            systemQuantity: import("generated/prisma/runtime/library").Decimal;
            totalDifference: import("generated/prisma/runtime/library").Decimal | null;
            stockCountId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
    private applyStockCountAdjustments;
    removeStockCount(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import("../../generated/prisma").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import("../../generated/prisma").$Enums.StockCountType;
        countedBy: string[];
    }>;
}
