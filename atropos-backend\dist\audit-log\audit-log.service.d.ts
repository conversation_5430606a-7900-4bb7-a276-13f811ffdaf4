import { PrismaService } from '../prisma/prisma.service';
import { CreateAuditLogDto } from './dto/create-audit-log.dto';
import { UpdateAuditLogDto } from './dto/update-audit-log.dto';
export declare class AuditLogService {
    private prisma;
    constructor(prisma: PrismaService);
    createAuditLog(data: CreateAuditLogDto): Promise<{
        id: string;
        userId: string | null;
        ipAddress: string | null;
        timestamp: Date;
        action: string;
        entityType: string | null;
        entityId: string | null;
        details: import("generated/prisma/runtime/library").JsonValue | null;
        userAgent: string | null;
    }>;
    findAllAuditLogs(userId?: string, action?: string, entityType?: string, entityId?: string, startDate?: Date, endDate?: Date): Promise<({
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
    } & {
        id: string;
        userId: string | null;
        ipAddress: string | null;
        timestamp: Date;
        action: string;
        entityType: string | null;
        entityId: string | null;
        details: import("generated/prisma/runtime/library").JsonValue | null;
        userAgent: string | null;
    })[]>;
    findOneAuditLog(id: string): Promise<{
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
    } & {
        id: string;
        userId: string | null;
        ipAddress: string | null;
        timestamp: Date;
        action: string;
        entityType: string | null;
        entityId: string | null;
        details: import("generated/prisma/runtime/library").JsonValue | null;
        userAgent: string | null;
    }>;
    updateAuditLog(id: string, data: UpdateAuditLogDto): Promise<void>;
    removeAuditLog(id: string): Promise<void>;
}
