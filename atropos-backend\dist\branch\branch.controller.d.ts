import { BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
export declare class BranchController {
    private readonly branchService;
    constructor(branchService: BranchService);
    create(createBranchDto: CreateBranchDto): Promise<{
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
    findAll(companyId?: string): Promise<({
        company: {
            name: string;
            taxNumber: string;
            taxOffice: string;
            address: string;
            phone: string;
            email: string;
            logo: string | null;
            website: string | null;
            eArchiveUsername: string | null;
            eArchivePassword: string | null;
            eInvoiceUsername: string | null;
            eInvoicePassword: string | null;
            smsProvider: string | null;
            smsApiKey: string | null;
            smsApiSecret: string | null;
            smsSenderName: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            name: string;
            taxNumber: string;
            taxOffice: string;
            address: string;
            phone: string;
            email: string;
            logo: string | null;
            website: string | null;
            eArchiveUsername: string | null;
            eArchivePassword: string | null;
            eInvoiceUsername: string | null;
            eInvoicePassword: string | null;
            smsProvider: string | null;
            smsApiKey: string | null;
            smsApiSecret: string | null;
            smsSenderName: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
    update(id: string, updateBranchDto: UpdateBranchDto): Promise<{
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
    remove(id: string): Promise<{
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
}
