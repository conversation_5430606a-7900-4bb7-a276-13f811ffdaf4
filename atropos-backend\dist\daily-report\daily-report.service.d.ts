import { PrismaService } from '../prisma/prisma.service';
import { CreateDailyReportDto } from './dto/create-daily-report.dto';
import { UpdateDailyReportDto } from './dto/update-daily-report.dto';
export declare class DailyReportService {
    private prisma;
    constructor(prisma: PrismaService);
    generateReportData(branchId: string, reportDate: Date): Promise<{
        reportDate: Date;
        reportNo: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: number;
        grossSales: number;
        totalDiscount: number;
        totalServiceCharge: number;
        netSales: number;
        totalTax: number;
        totalSales: number;
        cashSales: number;
        creditCardSales: number;
        debitCardSales: number;
        mealCardSales: number;
        otherSales: number;
        totalReturns: number;
        totalCancellations: number;
        openingBalance: number;
        totalCashIn: number;
        totalCashOut: number;
        expectedBalance: number;
        actualBalance: null;
        difference: null;
        taxBreakdown: {
            [taxId: string]: {
                name: string;
                rate: number;
                base: number;
                amount: number;
            };
        };
        categoryBreakdown: {
            [categoryId: string]: {
                name: string;
                grossSales: number;
            };
        };
        hourlyBreakdown: {
            [hour: string]: number;
        };
    }>;
    private generateReportNumber;
    createDailyReport(data: CreateDailyReportDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
    findAllDailyReports(branchId?: string, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    })[]>;
    findOneDailyReport(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
    updateDailyReport(id: string, data: UpdateDailyReportDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
    removeDailyReport(id: string): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        approvedBy: string | null;
        approvedAt: Date | null;
        reportDate: Date;
        createdBy: string;
        totalOrders: number;
        totalItems: number;
        totalCustomers: number;
        averageTicket: import("generated/prisma/runtime/library").Decimal;
        grossSales: import("generated/prisma/runtime/library").Decimal;
        totalDiscount: import("generated/prisma/runtime/library").Decimal;
        totalServiceCharge: import("generated/prisma/runtime/library").Decimal;
        netSales: import("generated/prisma/runtime/library").Decimal;
        totalTax: import("generated/prisma/runtime/library").Decimal;
        totalSales: import("generated/prisma/runtime/library").Decimal;
        cashSales: import("generated/prisma/runtime/library").Decimal;
        creditCardSales: import("generated/prisma/runtime/library").Decimal;
        debitCardSales: import("generated/prisma/runtime/library").Decimal;
        mealCardSales: import("generated/prisma/runtime/library").Decimal;
        otherSales: import("generated/prisma/runtime/library").Decimal;
        totalReturns: import("generated/prisma/runtime/library").Decimal;
        totalCancellations: import("generated/prisma/runtime/library").Decimal;
        openingBalance: import("generated/prisma/runtime/library").Decimal;
        totalCashIn: import("generated/prisma/runtime/library").Decimal;
        totalCashOut: import("generated/prisma/runtime/library").Decimal;
        expectedBalance: import("generated/prisma/runtime/library").Decimal;
        actualBalance: import("generated/prisma/runtime/library").Decimal;
        difference: import("generated/prisma/runtime/library").Decimal;
        taxBreakdown: import("generated/prisma/runtime/library").JsonValue;
        categoryBreakdown: import("generated/prisma/runtime/library").JsonValue;
        hourlyBreakdown: import("generated/prisma/runtime/library").JsonValue;
        zReportNo: string | null;
        fiscalId: string | null;
        printedAt: Date | null;
        emailedAt: Date | null;
        reportNo: string;
    }>;
}
