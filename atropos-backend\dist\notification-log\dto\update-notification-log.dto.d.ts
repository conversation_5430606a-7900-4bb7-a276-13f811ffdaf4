import { CreateNotificationLogDto } from './create-notification-log.dto';
import { NotificationStatus } from '../../../generated/prisma';
declare const UpdateNotificationLogDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateNotificationLogDto>>;
export declare class UpdateNotificationLogDto extends UpdateNotificationLogDto_base {
    status?: NotificationStatus;
    sentAt?: Date;
    deliveredAt?: Date;
    readAt?: Date;
}
export {};
