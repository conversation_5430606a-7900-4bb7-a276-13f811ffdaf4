"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderLogController = void 0;
const common_1 = require("@nestjs/common");
const order_log_service_1 = require("./order-log.service");
const create_order_log_dto_1 = require("./dto/create-order-log.dto");
const update_order_log_dto_1 = require("./dto/update-order-log.dto");
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
let OrderLogController = class OrderLogController {
    orderLogService;
    constructor(orderLogService) {
        this.orderLogService = orderLogService;
    }
    create(createOrderLogDto) {
        return this.orderLogService.createOrderLog(createOrderLogDto);
    }
    findAll(orderId, userId, action, startDate, endDate) {
        return this.orderLogService.findAllOrderLogs(orderId, userId, action, startDate, endDate);
    }
    findOne(id) {
        return this.orderLogService.findOneOrderLog(id);
    }
    update(id, updateOrderLogDto) {
        return this.orderLogService.updateOrderLog(id, updateOrderLogDto);
    }
    remove(id) {
        return this.orderLogService.removeOrderLog(id);
    }
};
exports.OrderLogController = OrderLogController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_order_log_dto_1.CreateOrderLogDto]),
    __metadata("design:returntype", void 0)
], OrderLogController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('orderId')),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('action')),
    __param(3, (0, common_1.Query)('startDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __param(4, (0, common_1.Query)('endDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Date,
        Date]),
    __metadata("design:returntype", void 0)
], OrderLogController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OrderLogController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_order_log_dto_1.UpdateOrderLogDto]),
    __metadata("design:returntype", void 0)
], OrderLogController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OrderLogController.prototype, "remove", null);
exports.OrderLogController = OrderLogController = __decorate([
    (0, common_1.Controller)('order-log'),
    __metadata("design:paramtypes", [order_log_service_1.OrderLogService])
], OrderLogController);
//# sourceMappingURL=order-log.controller.js.map