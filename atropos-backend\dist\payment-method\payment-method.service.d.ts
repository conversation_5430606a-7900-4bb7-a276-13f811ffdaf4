import { PrismaService } from '../prisma/prisma.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
export declare class PaymentMethodService {
    private prisma;
    constructor(prisma: PrismaService);
    createPaymentMethod(data: CreatePaymentMethodDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
    findAllPaymentMethods(companyId?: string): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    })[]>;
    findOnePaymentMethod(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
    updatePaymentMethod(id: string, data: UpdatePaymentMethodDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
    removePaymentMethod(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
}
