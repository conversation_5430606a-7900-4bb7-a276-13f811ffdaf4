export type { TickDetails } from '@zag-js/timer';
export { TimerActionTrigger as ActionTrigger, type TimerActionTriggerBaseProps as ActionTriggerBaseProps, type TimerActionTriggerProps as ActionTriggerProps, } from './timer-action-trigger';
export { TimerArea as Area, type TimerAreaBaseProps as AreaBaseProps, type TimerAreaProps as AreaProps, } from './timer-area';
export { TimerContext as Context, type TimerContextProps as ContextProps } from './timer-context';
export { TimerControl as Control, type TimerControlBaseProps as ControlBaseProps, type TimerControlProps as ControlProps, } from './timer-control';
export { TimerItem as Item, type TimerItemBaseProps as ItemBaseProps, type TimerItemProps as ItemProps, } from './timer-item';
export { TimerRoot as Root, type TimerRootBaseProps as RootBaseProps, type TimerRootProps as RootProps, } from './timer-root';
export { TimerRootProvider as RootProvider, type TimerRootProviderProps as RootProviderBaseProps, type TimerRootProviderBaseProps as RootProviderProps, } from './timer-root-provider';
export { TimerSeparator as Separator, type TimerSeparatorBaseProps as SeparatorBaseProps, type TimerSeparatorProps as SeparatorProps, } from './timer-separator';
