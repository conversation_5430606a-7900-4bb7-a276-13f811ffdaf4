import { StockCountStatus } from '../../../generated/prisma';
export declare class UpdateStockCountItemDto {
    id?: string;
    inventoryItemId: string;
    countedQuantity: number;
    note?: string;
}
export declare class UpdateStockCountDto {
    branchId?: string;
    countDate?: Date;
    countType?: any;
    status?: StockCountStatus;
    note?: string;
    startedAt?: Date;
    completedAt?: Date;
    approvedAt?: Date;
    createdBy?: string;
    countedBy?: string[];
    approvedBy?: string;
    items?: UpdateStockCountItemDto[];
}
