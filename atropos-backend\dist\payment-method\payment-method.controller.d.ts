import { PaymentMethodService } from './payment-method.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
export declare class PaymentMethodController {
    private readonly paymentMethodService;
    constructor(paymentMethodService: PaymentMethodService);
    create(createPaymentMethodDto: CreatePaymentMethodDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("../../generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
    findAll(companyId?: string): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("../../generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("../../generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
    update(id: string, updatePaymentMethodDto: UpdatePaymentMethodDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("../../generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
    remove(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        displayOrder: number;
        type: import("../../generated/prisma").$Enums.PaymentMethodType;
        merchantId: string | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        minAmount: import("generated/prisma/runtime/library").Decimal | null;
        maxAmount: import("generated/prisma/runtime/library").Decimal | null;
        requiresApproval: boolean;
        requiresReference: boolean;
        providerName: string | null;
        terminalId: string | null;
    }>;
}
