import { PrismaService } from '../prisma/prisma.service';
import { CreateTableAreaDto } from './dto/create-table-area.dto';
import { UpdateTableAreaDto } from './dto/update-table-area.dto';
export declare class TableAreaService {
    private prisma;
    constructor(prisma: PrismaService);
    createTableArea(data: CreateTableAreaDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
    findAllTableAreas(branchId?: string): Promise<({
        branch: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    })[]>;
    findOneTableArea(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
    updateTableArea(id: string, data: UpdateTableAreaDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
    removeTableArea(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        active: boolean;
        branchId: string;
        version: number;
        description: string | null;
        displayOrder: number;
        smokingAllowed: boolean;
    }>;
}
