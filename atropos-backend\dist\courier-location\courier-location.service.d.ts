import { PrismaService } from '../prisma/prisma.service';
import { CreateCourierLocationDto } from './dto/create-courier-location.dto';
import { UpdateCourierLocationDto } from './dto/update-courier-location.dto';
export declare class CourierLocationService {
    private prisma;
    constructor(prisma: PrismaService);
    createCourierLocation(data: CreateCourierLocationDto): Promise<{
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
    findAllCourierLocations(courierId?: string, branchId?: string, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            name: string;
            id: string;
        };
        courier: {
            id: string;
            firstName: string;
            lastName: string;
            employeeCode: string | null;
        };
    } & {
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    })[]>;
    findOneCourierLocation(id: string): Promise<{
        branch: {
            name: string;
            id: string;
        };
        courier: {
            id: string;
            firstName: string;
            lastName: string;
            employeeCode: string | null;
        };
    } & {
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
    updateCourierLocation(id: string, data: UpdateCourierLocationDto): Promise<{
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
    removeCourierLocation(id: string): Promise<{
        id: string;
        latitude: number;
        longitude: number;
        branchId: string;
        courierId: string;
        expiresAt: Date;
        timestamp: Date;
    }>;
}
