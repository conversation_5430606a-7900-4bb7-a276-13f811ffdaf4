"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableAreaService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let TableAreaService = class TableAreaService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createTableArea(data) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        const existingTableArea = await this.prisma.tableArea.findFirst({
            where: {
                branchId: data.branchId,
                name: data.name,
                deletedAt: null,
            },
        });
        if (existingTableArea) {
            throw new common_1.ConflictException(`Table area with name "${data.name}" already exists for this branch.`);
        }
        return this.prisma.tableArea.create({ data });
    }
    async findAllTableAreas(branchId) {
        return this.prisma.tableArea.findMany({
            where: { branchId: branchId || undefined, deletedAt: null },
            include: { branch: { select: { id: true, name: true } } },
            orderBy: { displayOrder: 'asc' },
        });
    }
    async findOneTableArea(id) {
        const tableArea = await this.prisma.tableArea.findUnique({
            where: { id, deletedAt: null },
            include: { branch: { select: { id: true, name: true } } },
        });
        if (!tableArea) {
            throw new common_1.NotFoundException(`Table area with ID "${id}" not found.`);
        }
        return tableArea;
    }
    async updateTableArea(id, data) {
        if (data.branchId) {
            const branchExists = await this.prisma.branch.findUnique({
                where: { id: data.branchId, deletedAt: null },
            });
            if (!branchExists) {
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
            }
        }
        if (data.name) {
            const currentTableArea = await this.findOneTableArea(id);
            const existingTableArea = await this.prisma.tableArea.findFirst({
                where: {
                    branchId: data.branchId || currentTableArea.branchId,
                    name: data.name,
                    id: { not: id },
                    deletedAt: null
                }
            });
            if (existingTableArea) {
                throw new common_1.ConflictException(`Table area with name "${data.name}" already exists for this branch.`);
            }
        }
        try {
            return await this.prisma.tableArea.update({
                where: { id, deletedAt: null },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Table area with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeTableArea(id) {
        const tablesCount = await this.prisma.table.count({
            where: { areaId: id, deletedAt: null }
        });
        if (tablesCount > 0) {
            throw new common_1.ConflictException(`Table area with ID "${id}" cannot be deleted because it has ${tablesCount} active tables.`);
        }
        try {
            return await this.prisma.tableArea.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Table area with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.TableAreaService = TableAreaService;
exports.TableAreaService = TableAreaService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TableAreaService);
//# sourceMappingURL=table-area.service.js.map