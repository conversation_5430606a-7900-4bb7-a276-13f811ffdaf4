export declare class CreateCustomerDto {
    firstName?: string;
    lastName?: string;
    companyName?: string;
    title?: string;
    taxNumber?: string;
    taxOffice?: string;
    phone: string;
    phone2?: string;
    email?: string;
    address?: string;
    district?: string;
    city?: string;
    country?: string;
    postalCode?: string;
    birthDate?: Date;
    gender?: string;
    marketingConsent?: boolean;
    smsConsent?: boolean;
    emailConsent?: boolean;
    loyaltyPoints?: number;
    totalSpent?: number;
    orderCount?: number;
    lastOrderDate?: Date;
    currentDebt?: number;
    creditLimit?: number;
    paymentTerm?: number;
    segment?: string;
    tags?: string[];
    notes?: string;
    source?: string;
    referredBy?: string;
    blacklisted?: boolean;
    blacklistReason?: string;
    active?: boolean;
}
