import { AuditLogService } from './audit-log.service';
import { CreateAuditLogDto } from './dto/create-audit-log.dto';
import { UpdateAuditLogDto } from './dto/update-audit-log.dto';
export declare class AuditLogController {
    private readonly auditLogService;
    constructor(auditLogService: AuditLogService);
    create(createAuditLogDto: CreateAuditLogDto): Promise<{
        id: string;
        userId: string | null;
        ipAddress: string | null;
        timestamp: Date;
        action: string;
        entityType: string | null;
        entityId: string | null;
        details: import("generated/prisma/runtime/library").JsonValue | null;
        userAgent: string | null;
    }>;
    findAll(userId?: string, action?: string, entityType?: string, entityId?: string, startDate?: Date, endDate?: Date): Promise<({
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
    } & {
        id: string;
        userId: string | null;
        ipAddress: string | null;
        timestamp: Date;
        action: string;
        entityType: string | null;
        entityId: string | null;
        details: import("generated/prisma/runtime/library").JsonValue | null;
        userAgent: string | null;
    })[]>;
    findOne(id: string): Promise<{
        user: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
    } & {
        id: string;
        userId: string | null;
        ipAddress: string | null;
        timestamp: Date;
        action: string;
        entityType: string | null;
        entityId: string | null;
        details: import("generated/prisma/runtime/library").JsonValue | null;
        userAgent: string | null;
    }>;
    update(id: string, updateAuditLogDto: UpdateAuditLogDto): Promise<void>;
    remove(id: string): Promise<void>;
}
