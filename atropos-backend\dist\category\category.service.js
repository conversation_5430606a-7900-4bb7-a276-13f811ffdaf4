"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CategoryService = class CategoryService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createCategory(data) {
        const existingCategory = await this.prisma.category.findFirst({
            where: {
                companyId: data.companyId,
                name: data.name,
                deletedAt: null
            },
        });
        if (existingCategory) {
            throw new common_1.ConflictException(`Category with name "${data.name}" already exists for this company.`);
        }
        if (data.parentId) {
            const parentExists = await this.prisma.category.findUnique({
                where: { id: data.parentId, deletedAt: null },
            });
            if (!parentExists) {
                throw new common_1.NotFoundException(`Parent category with ID "${data.parentId}" not found.`);
            }
        }
        return this.prisma.category.create({ data });
    }
    async findAllCategories(companyId, parentId) {
        return this.prisma.category.findMany({
            where: {
                companyId: companyId || undefined,
                parentId: parentId === null ? null : (parentId || undefined),
                deletedAt: null,
            },
            include: {
                parent: {
                    select: { id: true, name: true }
                },
                children: {
                    select: { id: true, name: true }
                },
            },
            orderBy: { displayOrder: 'asc' },
        });
    }
    async findOneCategory(id) {
        const category = await this.prisma.category.findUnique({
            where: { id, deletedAt: null },
            include: {
                parent: {
                    select: { id: true, name: true }
                },
                children: {
                    select: { id: true, name: true }
                },
            },
        });
        if (!category) {
            throw new common_1.NotFoundException(`Category with ID "${id}" not found.`);
        }
        return category;
    }
    async updateCategory(id, data) {
        if (data.parentId !== undefined && data.parentId !== null) {
            const parentExists = await this.prisma.category.findUnique({
                where: { id: data.parentId, deletedAt: null },
            });
            if (!parentExists) {
                throw new common_1.NotFoundException(`Parent category with ID "${data.parentId}" not found.`);
            }
        }
        if (data.name) {
            const currentCategory = await this.findOneCategory(id);
            const existingCategory = await this.prisma.category.findFirst({
                where: {
                    companyId: currentCategory.companyId,
                    name: data.name,
                    id: { not: id },
                    deletedAt: null
                }
            });
            if (existingCategory) {
                throw new common_1.ConflictException(`Category with name "${data.name}" already exists for this company.`);
            }
        }
        try {
            return await this.prisma.category.update({
                where: { id, deletedAt: null },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Category with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeCategory(id) {
        const productsCount = await this.prisma.product.count({
            where: { categoryId: id, deletedAt: null }
        });
        if (productsCount > 0) {
            throw new common_1.ConflictException(`Category with ID "${id}" cannot be deleted because it has ${productsCount} active products.`);
        }
        const childCategoriesCount = await this.prisma.category.count({
            where: { parentId: id, deletedAt: null }
        });
        if (childCategoriesCount > 0) {
            throw new common_1.ConflictException(`Category with ID "${id}" cannot be deleted because it has ${childCategoriesCount} active subcategories.`);
        }
        try {
            return await this.prisma.category.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false, showInMenu: false, showInKitchen: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Category with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.CategoryService = CategoryService;
exports.CategoryService = CategoryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CategoryService);
//# sourceMappingURL=category.service.js.map