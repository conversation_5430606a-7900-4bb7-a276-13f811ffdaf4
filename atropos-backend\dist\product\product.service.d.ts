import { PrismaService } from '../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
export declare class ProductService {
    private prisma;
    constructor(prisma: PrismaService);
    createProduct(data: CreateProductDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        description: string | null;
        image: string | null;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        categoryId: string;
        taxId: string;
        barcode: string | null;
        shortDescription: string | null;
        images: string[];
        basePrice: import("generated/prisma/runtime/library").Decimal;
        costPrice: import("generated/prisma/runtime/library").Decimal | null;
        profitMargin: import("generated/prisma/runtime/library").Decimal | null;
        trackStock: boolean;
        unit: import("generated/prisma").$Enums.ProductUnit;
        criticalStock: import("generated/prisma/runtime/library").Decimal | null;
        available: boolean;
        sellable: boolean;
        calories: number | null;
        allergens: string[];
        hasVariants: boolean;
        hasModifiers: boolean;
        featured: boolean;
        syncId: string | null;
        lastSyncAt: Date | null;
    }>;
    findAllProducts(companyId?: string, categoryId?: string): Promise<({
        category: {
            name: string;
            id: string;
        };
        tax: {
            name: string;
            id: string;
            rate: import("generated/prisma/runtime/library").Decimal;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        description: string | null;
        image: string | null;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        categoryId: string;
        taxId: string;
        barcode: string | null;
        shortDescription: string | null;
        images: string[];
        basePrice: import("generated/prisma/runtime/library").Decimal;
        costPrice: import("generated/prisma/runtime/library").Decimal | null;
        profitMargin: import("generated/prisma/runtime/library").Decimal | null;
        trackStock: boolean;
        unit: import("generated/prisma").$Enums.ProductUnit;
        criticalStock: import("generated/prisma/runtime/library").Decimal | null;
        available: boolean;
        sellable: boolean;
        calories: number | null;
        allergens: string[];
        hasVariants: boolean;
        hasModifiers: boolean;
        featured: boolean;
        syncId: string | null;
        lastSyncAt: Date | null;
    })[]>;
    findOneProduct(id: string): Promise<{
        category: {
            name: string;
            id: string;
        };
        tax: {
            name: string;
            id: string;
            rate: import("generated/prisma/runtime/library").Decimal;
        };
        variants: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            code: string;
            active: boolean;
            version: number;
            displayOrder: number;
            barcode: string | null;
            costPrice: import("generated/prisma/runtime/library").Decimal | null;
            productId: string;
            sku: string | null;
            price: import("generated/prisma/runtime/library").Decimal;
        }[];
        modifierGroups: {
            displayOrder: number;
            productId: string;
            modifierGroupId: string;
        }[];
        recipes: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            active: boolean;
            version: number;
            preparationTime: number | null;
            productId: string;
            yield: import("generated/prisma/runtime/library").Decimal;
            preparationSteps: string | null;
        }[];
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        description: string | null;
        image: string | null;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        categoryId: string;
        taxId: string;
        barcode: string | null;
        shortDescription: string | null;
        images: string[];
        basePrice: import("generated/prisma/runtime/library").Decimal;
        costPrice: import("generated/prisma/runtime/library").Decimal | null;
        profitMargin: import("generated/prisma/runtime/library").Decimal | null;
        trackStock: boolean;
        unit: import("generated/prisma").$Enums.ProductUnit;
        criticalStock: import("generated/prisma/runtime/library").Decimal | null;
        available: boolean;
        sellable: boolean;
        calories: number | null;
        allergens: string[];
        hasVariants: boolean;
        hasModifiers: boolean;
        featured: boolean;
        syncId: string | null;
        lastSyncAt: Date | null;
    }>;
    updateProduct(id: string, data: UpdateProductDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        description: string | null;
        image: string | null;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        categoryId: string;
        taxId: string;
        barcode: string | null;
        shortDescription: string | null;
        images: string[];
        basePrice: import("generated/prisma/runtime/library").Decimal;
        costPrice: import("generated/prisma/runtime/library").Decimal | null;
        profitMargin: import("generated/prisma/runtime/library").Decimal | null;
        trackStock: boolean;
        unit: import("generated/prisma").$Enums.ProductUnit;
        criticalStock: import("generated/prisma/runtime/library").Decimal | null;
        available: boolean;
        sellable: boolean;
        calories: number | null;
        allergens: string[];
        hasVariants: boolean;
        hasModifiers: boolean;
        featured: boolean;
        syncId: string | null;
        lastSyncAt: Date | null;
    }>;
    removeProduct(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        active: boolean;
        version: number;
        description: string | null;
        image: string | null;
        preparationTime: number | null;
        displayOrder: number;
        showInMenu: boolean;
        categoryId: string;
        taxId: string;
        barcode: string | null;
        shortDescription: string | null;
        images: string[];
        basePrice: import("generated/prisma/runtime/library").Decimal;
        costPrice: import("generated/prisma/runtime/library").Decimal | null;
        profitMargin: import("generated/prisma/runtime/library").Decimal | null;
        trackStock: boolean;
        unit: import("generated/prisma").$Enums.ProductUnit;
        criticalStock: import("generated/prisma/runtime/library").Decimal | null;
        available: boolean;
        sellable: boolean;
        calories: number | null;
        allergens: string[];
        hasVariants: boolean;
        hasModifiers: boolean;
        featured: boolean;
        syncId: string | null;
        lastSyncAt: Date | null;
    }>;
}
