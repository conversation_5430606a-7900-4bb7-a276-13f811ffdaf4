import { OnlineProductMappingService } from './online-product-mapping.service';
import { CreateOnlineProductMappingDto } from './dto/create-online-product-mapping.dto';
import { UpdateOnlineProductMappingDto } from './dto/update-online-product-mapping.dto';
export declare class OnlineProductMappingController {
    private readonly onlineProductMappingService;
    constructor(onlineProductMappingService: OnlineProductMappingService);
    create(createOnlineProductMappingDto: CreateOnlineProductMappingDto): Promise<{
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
    findAll(platformId?: string, productId?: string): Promise<({
        product: {
            name: string;
            id: string;
            code: string;
            basePrice: import("generated/prisma/runtime/library").Decimal;
        };
        platform: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    })[]>;
    findOne(id: string): Promise<{
        product: {
            name: string;
            id: string;
            code: string;
            basePrice: import("generated/prisma/runtime/library").Decimal;
        };
        platform: {
            name: string;
            id: string;
            code: string;
        };
    } & {
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
    update(id: string, updateOnlineProductMappingDto: UpdateOnlineProductMappingDto): Promise<{
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
    remove(id: string): Promise<{
        priceOverride: import("generated/prisma/runtime/library").Decimal | null;
        id: string;
        productId: string;
        platformId: string;
        platformProductId: string;
        platformBarcode: string | null;
        isActive: boolean;
    }>;
}
