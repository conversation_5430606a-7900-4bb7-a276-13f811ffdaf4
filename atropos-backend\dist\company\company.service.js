"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CompanyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CompanyService = CompanyService_1 = class CompanyService {
    prisma;
    logger = new common_1.Logger(CompanyService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createCompany(data) {
        this.logger.log(`Attempting to create a new company: ${data.name}`);
        try {
            const existingCompany = await this.prisma.company.findUnique({
                where: { taxNumber: data.taxNumber },
            });
            if (existingCompany) {
                this.logger.warn(`Company creation failed: Tax number ${data.taxNumber} already exists.`);
                throw new common_1.ConflictException(`Company with tax number "${data.taxNumber}" already exists.`);
            }
            const company = await this.prisma.company.create({ data });
            this.logger.log(`Company "${company.name}" created successfully with ID: ${company.id}`);
            return company;
        }
        catch (error) {
            this.logger.error(`Error creating company "${data.name}": ${error.message}`, error.stack);
            throw error;
        }
    }
    async findAllCompanies() {
        this.logger.verbose('Fetching all companies from the database.');
        return this.prisma.company.findMany();
    }
    async findOneCompany(id) {
        this.logger.debug(`Attempting to find company with ID: ${id}`);
        const company = await this.prisma.company.findUnique({
            where: { id },
        });
        if (!company) {
            this.logger.warn(`Company with ID "${id}" not found.`);
            throw new common_1.NotFoundException(`Company with ID "${id}" not found.`);
        }
        return company;
    }
    async updateCompany(id, data) {
        this.logger.log(`Attempting to update company with ID: ${id}`);
        try {
            const company = await this.prisma.company.update({
                where: { id },
                data,
            });
            this.logger.log(`Company with ID "${id}" updated successfully`);
            return company;
        }
        catch (error) {
            if (error.code === 'P2025') {
                this.logger.warn(`Update failed: Company with ID "${id}" not found.`);
                throw new common_1.NotFoundException(`Company with ID "${id}" not found.`);
            }
            this.logger.error(`Error updating company with ID "${id}": ${error.message}`, error.stack);
            throw error;
        }
    }
    async removeCompany(id) {
        this.logger.log(`Attempting to delete company with ID: ${id}`);
        try {
            const company = await this.prisma.company.delete({
                where: { id },
            });
            this.logger.log(`Company with ID "${id}" deleted successfully`);
            return company;
        }
        catch (error) {
            if (error.code === 'P2025') {
                this.logger.warn(`Delete failed: Company with ID "${id}" not found.`);
                throw new common_1.NotFoundException(`Company with ID "${id}" not found.`);
            }
            this.logger.error(`Error deleting company with ID "${id}": ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.CompanyService = CompanyService;
exports.CompanyService = CompanyService = CompanyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CompanyService);
//# sourceMappingURL=company.service.js.map