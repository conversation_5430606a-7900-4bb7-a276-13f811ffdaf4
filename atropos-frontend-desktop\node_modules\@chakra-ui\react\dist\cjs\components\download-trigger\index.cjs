"use strict";
"use client";
'use strict';

var downloadTrigger = require('@ark-ui/react/download-trigger');
var createRecipeContext = require('../../styled-system/create-recipe-context.cjs');

const { withContext } = createRecipeContext.createRecipeContext({ key: "downloadTrigger" });
const DownloadTrigger = withContext(downloadTrigger.DownloadTrigger, { forwardAsChild: true });

exports.DownloadTrigger = DownloadTrigger;
