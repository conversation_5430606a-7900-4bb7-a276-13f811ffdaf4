"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyCardService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
let LoyaltyCardService = class LoyaltyCardService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createLoyaltyCard(data) {
        const customerExists = await this.prisma.customer.findUnique({
            where: { id: data.customerId, deletedAt: null },
        });
        if (!customerExists) {
            throw new common_1.NotFoundException(`Customer with ID "${data.customerId}" not found.`);
        }
        const existingCardByCustomer = await this.prisma.loyaltyCard.findUnique({
            where: { customerId: data.customerId },
        });
        if (existingCardByCustomer) {
            throw new common_1.ConflictException(`Loyalty card already exists for customer with ID "${data.customerId}".`);
        }
        const existingCardByNumber = await this.prisma.loyaltyCard.findUnique({
            where: { cardNumber: data.cardNumber },
        });
        if (existingCardByNumber) {
            throw new common_1.ConflictException(`Loyalty card with number "${data.cardNumber}" already exists.`);
        }
        let hashedPin;
        if (data.pin) {
            hashedPin = await bcrypt.hash(data.pin, 10);
        }
        const createdCard = await this.prisma.loyaltyCard.create({
            data: {
                ...data,
                pin: hashedPin,
                points: data.points !== undefined ? data.points : 0,
                totalEarnedPoints: data.totalEarnedPoints !== undefined ? data.totalEarnedPoints : 0,
                totalSpentPoints: data.totalSpentPoints !== undefined ? data.totalSpentPoints : 0,
                balance: data.balance !== undefined ? parseFloat(data.balance.toFixed(2)) : 0,
                totalLoaded: data.totalLoaded !== undefined ? parseFloat(data.totalLoaded.toFixed(2)) : 0,
                discountRate: data.discountRate !== undefined ? parseFloat(data.discountRate.toFixed(2)) : 0,
                issuedAt: data.issuedAt || new Date(),
            },
        });
        const { pin, ...cardWithoutPin } = createdCard;
        return cardWithoutPin;
    }
    async findAllLoyaltyCards(customerId, cardNumber, cardType) {
        return this.prisma.loyaltyCard.findMany({
            where: {
                customerId: customerId || undefined,
                cardNumber: cardNumber || undefined,
                cardType: cardType || undefined,
                active: true,
            },
            select: {
                id: true,
                customerId: true,
                cardNumber: true,
                cardType: true,
                points: true,
                totalEarnedPoints: true,
                totalSpentPoints: true,
                balance: true,
                totalLoaded: true,
                discountRate: true,
                issuedAt: true,
                activatedAt: true,
                expiresAt: true,
                blocked: true,
                blockReason: true,
                active: true,
                lastUsedAt: true,
                customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
            },
            orderBy: { id: 'desc' },
        });
    }
    async findOneLoyaltyCard(id) {
        const card = await this.prisma.loyaltyCard.findUnique({
            where: { id, active: true },
            select: {
                id: true,
                customerId: true,
                cardNumber: true,
                cardType: true,
                points: true,
                totalEarnedPoints: true,
                totalSpentPoints: true,
                balance: true,
                totalLoaded: true,
                discountRate: true,
                issuedAt: true,
                activatedAt: true,
                expiresAt: true,
                blocked: true,
                blockReason: true,
                active: true,
                lastUsedAt: true,
                customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
            },
        });
        if (!card) {
            throw new common_1.NotFoundException(`Loyalty card with ID "${id}" not found or not active.`);
        }
        return card;
    }
    async updateLoyaltyCard(id, data) {
        const existingCard = await this.findOneLoyaltyCard(id);
        if (data.customerId && data.customerId !== existingCard.customerId) {
            const customerExists = await this.prisma.customer.findUnique({ where: { id: data.customerId, deletedAt: null } });
            if (!customerExists) {
                throw new common_1.NotFoundException(`Customer with ID "${data.customerId}" not found.`);
            }
            const existingCardForNewCustomer = await this.prisma.loyaltyCard.findUnique({ where: { customerId: data.customerId } });
            if (existingCardForNewCustomer) {
                throw new common_1.ConflictException(`Loyalty card already exists for customer with ID "${data.customerId}".`);
            }
        }
        if (data.cardNumber && data.cardNumber !== existingCard.cardNumber) {
            const existingCardByNumber = await this.prisma.loyaltyCard.findUnique({
                where: { cardNumber: data.cardNumber },
            });
            if (existingCardByNumber && existingCardByNumber.id !== id) {
                throw new common_1.ConflictException(`Loyalty card with number "${data.cardNumber}" already exists.`);
            }
        }
        if (data.pin) {
            data.pin = await bcrypt.hash(data.pin, 10);
        }
        try {
            const updatedCard = await this.prisma.loyaltyCard.update({
                where: { id, active: true },
                data: {
                    ...data,
                    balance: data.balance !== undefined ? parseFloat(data.balance.toFixed(2)) : undefined,
                    totalLoaded: data.totalLoaded !== undefined ? parseFloat(data.totalLoaded.toFixed(2)) : undefined,
                    discountRate: data.discountRate !== undefined ? parseFloat(data.discountRate.toFixed(2)) : undefined,
                },
            });
            const { pin, ...cardWithoutPin } = updatedCard;
            return cardWithoutPin;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Loyalty card with ID "${id}" not found or not active.`);
            }
            throw error;
        }
    }
    async removeLoyaltyCard(id) {
        try {
            return await this.prisma.loyaltyCard.update({
                where: { id },
                data: { active: false, blocked: true, blockReason: 'Card removed/terminated by admin.' },
                select: { id: true, active: true, blocked: true, blockReason: true }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Loyalty card with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async addPoints(cardId, pointsToAdd) {
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: {
                points: { increment: pointsToAdd },
                totalEarnedPoints: { increment: pointsToAdd },
                lastUsedAt: new Date(),
            },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async spendPoints(cardId, pointsToSpend) {
        const card = await this.prisma.loyaltyCard.findUnique({ where: { id: cardId } });
        if (!card || card.points < pointsToSpend) {
            throw new common_1.BadRequestException('Insufficient loyalty points.');
        }
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: {
                points: { decrement: pointsToSpend },
                totalSpentPoints: { increment: pointsToSpend },
                lastUsedAt: new Date(),
            },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async addBalance(cardId, amountToAdd) {
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: {
                balance: { increment: parseFloat(amountToAdd.toFixed(2)) },
                totalLoaded: { increment: parseFloat(amountToAdd.toFixed(2)) },
                lastUsedAt: new Date(),
            },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async spendBalance(cardId, amountToSpend) {
        const card = await this.prisma.loyaltyCard.findUnique({ where: { id: cardId } });
        if (!card || parseFloat(card.balance.toString()) < amountToSpend) {
            throw new common_1.BadRequestException('Insufficient balance.');
        }
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: {
                balance: { decrement: parseFloat(amountToSpend.toFixed(2)) },
                lastUsedAt: new Date(),
            },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async blockCard(cardId, reason) {
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: { blocked: true, blockReason: reason },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async unblockCard(cardId) {
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: { blocked: false, blockReason: null },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async activateCard(cardId) {
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: { active: true, activatedAt: new Date() },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
    async deactivateCard(cardId) {
        const updatedCard = await this.prisma.loyaltyCard.update({
            where: { id: cardId },
            data: { active: false },
        });
        const { pin, ...cardWithoutPin } = updatedCard;
        return cardWithoutPin;
    }
};
exports.LoyaltyCardService = LoyaltyCardService;
exports.LoyaltyCardService = LoyaltyCardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], LoyaltyCardService);
//# sourceMappingURL=loyalty-card.service.js.map