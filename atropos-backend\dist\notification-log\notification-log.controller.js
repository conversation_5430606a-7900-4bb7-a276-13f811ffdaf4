"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationLogController = void 0;
const common_1 = require("@nestjs/common");
const notification_log_service_1 = require("./notification-log.service");
const create_notification_log_dto_1 = require("./dto/create-notification-log.dto");
const update_notification_log_dto_1 = require("./dto/update-notification-log.dto");
const prisma_1 = require("../../generated/prisma");
const parse_optional_enum_pipe_1 = require("../common/pipes/parse-optional-enum.pipe");
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
let NotificationLogController = class NotificationLogController {
    notificationLogService;
    constructor(notificationLogService) {
        this.notificationLogService = notificationLogService;
    }
    create(createNotificationLogDto) {
        return this.notificationLogService.createNotificationLog(createNotificationLogDto);
    }
    findAll(templateId, recipient, channel, status, startDate, endDate) {
        return this.notificationLogService.findAllNotificationLogs(templateId, recipient, channel, status, startDate, endDate);
    }
    findOne(id) {
        return this.notificationLogService.findOneNotificationLog(id);
    }
    update(id, updateNotificationLogDto) {
        return this.notificationLogService.updateNotificationLog(id, updateNotificationLogDto);
    }
    remove(id) {
        return this.notificationLogService.removeNotificationLog(id);
    }
};
exports.NotificationLogController = NotificationLogController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_notification_log_dto_1.CreateNotificationLogDto]),
    __metadata("design:returntype", void 0)
], NotificationLogController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('templateId')),
    __param(1, (0, common_1.Query)('recipient')),
    __param(2, (0, common_1.Query)('channel', new parse_optional_enum_pipe_1.ParseOptionalEnumPipe(prisma_1.NotificationChannel))),
    __param(3, (0, common_1.Query)('status', new parse_optional_enum_pipe_1.ParseOptionalEnumPipe(prisma_1.NotificationStatus))),
    __param(4, (0, common_1.Query)('startDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __param(5, (0, common_1.Query)('endDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Date,
        Date]),
    __metadata("design:returntype", void 0)
], NotificationLogController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationLogController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_notification_log_dto_1.UpdateNotificationLogDto]),
    __metadata("design:returntype", void 0)
], NotificationLogController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationLogController.prototype, "remove", null);
exports.NotificationLogController = NotificationLogController = __decorate([
    (0, common_1.Controller)('notification-log'),
    __metadata("design:paramtypes", [notification_log_service_1.NotificationLogService])
], NotificationLogController);
//# sourceMappingURL=notification-log.controller.js.map