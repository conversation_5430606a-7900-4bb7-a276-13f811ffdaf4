declare const InvoiceType: {
    readonly RECEIPT: "RECEIPT";
    readonly INVOICE: "INVOICE";
    readonly E_ARCHIVE: "E_ARCHIVE";
    readonly E_INVOICE: "E_INVOICE";
    readonly PROFORMA: "PROFORMA";
    readonly RETURN: "RETURN";
};
declare const EArchiveStatus: {
    readonly PENDING: "PENDING";
    readonly SENT: "SENT";
    readonly APPROVED: "APPROVED";
    readonly REJECTED: "REJECTED";
    readonly CANCELLED: "CANCELLED";
};
type InvoiceType = typeof InvoiceType[keyof typeof InvoiceType];
type EArchiveStatus = typeof EArchiveStatus[keyof typeof EArchiveStatus];
export declare class CreateInvoiceDto {
    orderId?: string;
    invoiceType: InvoiceType;
    serialNo: string;
    sequenceNo?: string;
    customerName?: string;
    taxNumber?: string;
    taxOffice?: string;
    address?: string;
    phone?: string;
    email?: string;
    subtotal?: number;
    discountAmount?: number;
    taxDetails?: any;
    taxAmount?: number;
    totalAmount?: number;
    totalAmountText?: string;
    uuid?: string;
    eArchiveStatus?: EArchiveStatus;
    eArchiveResponse?: any;
    isCancelled?: boolean;
    cancelReason?: string;
    cancelledInvoiceId?: string;
    pdfUrl?: string;
}
export {};
