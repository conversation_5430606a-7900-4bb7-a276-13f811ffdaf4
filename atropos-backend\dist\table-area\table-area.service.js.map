{"version": 3, "file": "table-area.service.js", "sourceRoot": "", "sources": ["../../src/table-area/table-area.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,2CAAkF;AAClF,6DAAyD;AAKlD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IACP;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,eAAe,CAAC,IAAwB;QAE5C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE;SAC9C,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,IAAI,CAAC,QAAQ,cAAc,CAAC,CAAC;QAC9E,CAAC;QAGD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YAC9D,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QACH,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,IAAI,CAAC,IAAI,mCAAmC,CAAC,CAAC;QACrG,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAiB;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,IAAI,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;YAC3D,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;YACzD,OAAO,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE;SACjC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU;QAC/B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;YAC9B,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;SAC1D,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,cAAc,CAAC,CAAC;QACvE,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU,EAAE,IAAwB;QAExD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;gBACrD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE;aAChD,CAAC,CAAC;YACH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,IAAI,CAAC,QAAQ,cAAc,CAAC,CAAC;YAChF,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;gBAC5D,KAAK,EAAE;oBACH,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ;oBACpD,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;oBACf,SAAS,EAAE,IAAI;iBAClB;aACJ,CAAC,CAAC;YACH,IAAI,iBAAiB,EAAE,CAAC;gBACpB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,IAAI,CAAC,IAAI,mCAAmC,CAAC,CAAC;YACvG,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC9B,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,cAAc,CAAC,CAAC;YACvE,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU;QAE9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9C,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACzC,CAAC,CAAC;QAEH,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,sCAAsC,WAAW,iBAAiB,CAAC,CAAC;QAC7H,CAAC;QAGD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC9B,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;aAC/C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,iCAAiC,CAAC,CAAC;YAC1F,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA7GY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,gBAAgB,CA6G5B"}