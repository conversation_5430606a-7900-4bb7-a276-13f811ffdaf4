"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
let UserService = class UserService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findByUsername(username) {
        return this.prisma.user.findUnique({
            where: { username },
        });
    }
    async createUser(data) {
        const existingUser = await this.prisma.user.findUnique({
            where: { username: data.username },
        });
        if (existingUser) {
            throw new common_1.ConflictException(`Username "${data.username}" already taken.`);
        }
        const hashedPassword = await bcrypt.hash(data.password, 10);
        let hashedPin;
        if (data.pin) {
            hashedPin = await bcrypt.hash(data.pin, 10);
        }
        return this.prisma.user.create({
            data: {
                ...data,
                password: hashedPassword,
                pin: hashedPin,
            },
        });
    }
    async findAllUsers(companyId, branchId) {
        return this.prisma.user.findMany({
            where: {
                companyId: companyId || undefined,
                branchId: branchId || undefined,
                deletedAt: null,
            },
            select: {
                id: true,
                companyId: true,
                branchId: true,
                username: true,
                firstName: true,
                lastName: true,
                email: true,
                phone: true,
                avatar: true,
                role: true,
                permissions: true,
                employeeCode: true,
                hireDate: true,
                birthDate: true,
                nationalId: true,
                vehicleType: true,
                vehiclePlate: true,
                active: true,
                lastLoginAt: true,
                failedLoginCount: true,
                lockedUntil: true,
                version: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: true,
            }
        });
    }
    async findOneUser(id) {
        const user = await this.prisma.user.findUnique({
            where: { id, deletedAt: null },
            select: {
                id: true,
                companyId: true,
                branchId: true,
                username: true,
                firstName: true,
                lastName: true,
                email: true,
                phone: true,
                avatar: true,
                role: true,
                permissions: true,
                employeeCode: true,
                hireDate: true,
                birthDate: true,
                nationalId: true,
                vehicleType: true,
                vehiclePlate: true,
                active: true,
                lastLoginAt: true,
                failedLoginCount: true,
                lockedUntil: true,
                version: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: true,
            }
        });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${id}" not found.`);
        }
        return user;
    }
    async updateUser(id, data) {
        if (data.password) {
            data.password = await bcrypt.hash(data.password, 10);
        }
        if (data.pin) {
            data.pin = await bcrypt.hash(data.pin, 10);
        }
        if (data.username) {
            const existingUserWithUsername = await this.prisma.user.findUnique({
                where: { username: data.username },
            });
            if (existingUserWithUsername && existingUserWithUsername.id !== id) {
                throw new common_1.ConflictException(`Username "${data.username}" already taken by another user.`);
            }
        }
        try {
            return await this.prisma.user.update({
                where: { id, deletedAt: null },
                data,
                select: {
                    id: true,
                    companyId: true,
                    branchId: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    phone: true,
                    avatar: true,
                    role: true,
                    permissions: true,
                    employeeCode: true,
                    hireDate: true,
                    birthDate: true,
                    nationalId: true,
                    vehicleType: true,
                    vehiclePlate: true,
                    active: true,
                    lastLoginAt: true,
                    failedLoginCount: true,
                    lockedUntil: true,
                    version: true,
                    createdAt: true,
                    updatedAt: true,
                    deletedAt: true,
                }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`User with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeUser(id) {
        try {
            return await this.prisma.user.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false },
                select: { id: true, deletedAt: true, active: true }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`User with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserService);
//# sourceMappingURL=user.service.js.map