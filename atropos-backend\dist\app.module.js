"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const config_1 = require("@nestjs/config");
const prisma_module_1 = require("./prisma/prisma.module");
const company_module_1 = require("./company/company.module");
const branch_module_1 = require("./branch/branch.module");
const user_module_1 = require("./user/user.module");
const category_module_1 = require("./category/category.module");
const tax_module_1 = require("./tax/tax.module");
const product_module_1 = require("./product/product.module");
const table_area_module_1 = require("./table-area/table-area.module");
const table_module_1 = require("./table/table.module");
const customer_module_1 = require("./customer/customer.module");
const order_module_1 = require("./order/order.module");
const payment_method_module_1 = require("./payment-method/payment-method.module");
const payment_module_1 = require("./payment/payment.module");
const cash_movement_module_1 = require("./cash-movement/cash-movement.module");
const daily_report_module_1 = require("./daily-report/daily-report.module");
const invoice_module_1 = require("./invoice/invoice.module");
const online_platform_module_1 = require("./online-platform/online-platform.module");
const online_product_mapping_module_1 = require("./online-product-mapping/online-product-mapping.module");
const online_order_module_1 = require("./online-order/online-order.module");
const inventory_item_module_1 = require("./inventory-item/inventory-item.module");
const recipe_module_1 = require("./recipe/recipe.module");
const stock_movement_module_1 = require("./stock-movement/stock-movement.module");
const stock_count_module_1 = require("./stock-count/stock-count.module");
const loyalty_card_module_1 = require("./loyalty-card/loyalty-card.module");
const loyalty_transaction_module_1 = require("./loyalty-transaction/loyalty-transaction.module");
const reservation_module_1 = require("./reservation/reservation.module");
const campaign_module_1 = require("./campaign/campaign.module");
const printer_group_module_1 = require("./printer-group/printer-group.module");
const printer_module_1 = require("./printer/printer.module");
const notification_template_module_1 = require("./notification-template/notification-template.module");
const notification_log_module_1 = require("./notification-log/notification-log.module");
const courier_location_module_1 = require("./courier-location/courier-location.module");
const price_override_module_1 = require("./price-override/price-override.module");
const table_merge_module_1 = require("./table-merge/table-merge.module");
const audit_log_module_1 = require("./audit-log/audit-log.module");
const order_log_module_1 = require("./order-log/order-log.module");
const task_module_1 = require("./task/task.module");
const auth_module_1 = require("./auth/auth.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({ isGlobal: true }),
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            company_module_1.CompanyModule,
            branch_module_1.BranchModule,
            user_module_1.UserModule,
            category_module_1.CategoryModule,
            tax_module_1.TaxModule,
            product_module_1.ProductModule,
            table_area_module_1.TableAreaModule,
            table_module_1.TableModule,
            customer_module_1.CustomerModule,
            order_module_1.OrderModule,
            payment_method_module_1.PaymentMethodModule,
            payment_module_1.PaymentModule,
            cash_movement_module_1.CashMovementModule,
            daily_report_module_1.DailyReportModule,
            invoice_module_1.InvoiceModule,
            online_platform_module_1.OnlinePlatformModule,
            online_product_mapping_module_1.OnlineProductMappingModule,
            online_order_module_1.OnlineOrderModule,
            inventory_item_module_1.InventoryItemModule,
            recipe_module_1.RecipeModule,
            stock_movement_module_1.StockMovementModule,
            stock_count_module_1.StockCountModule,
            loyalty_card_module_1.LoyaltyCardModule,
            loyalty_transaction_module_1.LoyaltyTransactionModule,
            reservation_module_1.ReservationModule,
            campaign_module_1.CampaignModule,
            printer_group_module_1.PrinterGroupModule,
            printer_module_1.PrinterModule,
            notification_template_module_1.NotificationTemplateModule,
            notification_log_module_1.NotificationLogModule,
            courier_location_module_1.CourierLocationModule,
            price_override_module_1.PriceOverrideModule,
            table_merge_module_1.TableMergeModule,
            audit_log_module_1.AuditLogModule,
            order_log_module_1.OrderLogModule,
            task_module_1.TaskModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map