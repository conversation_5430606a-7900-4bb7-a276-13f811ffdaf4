import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    create(createUserDto: CreateUserDto): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        password: string;
        pin: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue | null;
        refreshToken: string | null;
        lockedUntil: Date | null;
        version: number;
    }>;
    findAll(companyId?: string, branchId?: string): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue;
        lockedUntil: Date | null;
        version: number;
    }[]>;
    findOne(id: string): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue;
        lockedUntil: Date | null;
        version: number;
    }>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<{
        phone: string | null;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import("generated/prisma").$Enums.UserRole;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        permissions: import("generated/prisma/runtime/library").JsonValue;
        lockedUntil: Date | null;
        version: number;
    }>;
    remove(id: string): Promise<{
        id: string;
        deletedAt: Date | null;
        active: boolean;
    }>;
}
