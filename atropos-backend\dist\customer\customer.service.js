"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CustomerService = class CustomerService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createCustomer(data) {
        const existingCustomer = await this.prisma.customer.findUnique({
            where: { phone: data.phone },
        });
        if (existingCustomer) {
            throw new common_1.ConflictException(`Customer with phone number "${data.phone}" already exists.`);
        }
        return this.prisma.customer.create({ data });
    }
    async findAllCustomers(phone, email, taxNumber) {
        return this.prisma.customer.findMany({
            where: {
                phone: phone || undefined,
                email: email || undefined,
                taxNumber: taxNumber || undefined,
                deletedAt: null,
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneCustomer(id) {
        const customer = await this.prisma.customer.findUnique({
            where: { id, deletedAt: null },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID "${id}" not found.`);
        }
        return customer;
    }
    async updateCustomer(id, data) {
        if (data.phone) {
            const existingCustomer = await this.prisma.customer.findUnique({
                where: { phone: data.phone },
            });
            if (existingCustomer && existingCustomer.id !== id) {
                throw new common_1.ConflictException(`Phone number "${data.phone}" is already in use by another customer.`);
            }
        }
        try {
            return await this.prisma.customer.update({
                where: { id, deletedAt: null },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Customer with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeCustomer(id) {
        const activeOrdersCount = await this.prisma.order.count({
            where: { customerId: id, status: { notIn: ['COMPLETED', 'CANCELLED', 'RETURNED'] }, deletedAt: null }
        });
        if (activeOrdersCount > 0) {
            throw new common_1.ConflictException(`Customer with ID "${id}" cannot be deleted because they have ${activeOrdersCount} active orders.`);
        }
        const activeReservationsCount = await this.prisma.reservation.count({
            where: { customerId: id, status: { notIn: ['COMPLETED', 'CANCELLED', 'NO_SHOW'] } }
        });
        if (activeReservationsCount > 0) {
            throw new common_1.ConflictException(`Customer with ID "${id}" cannot be deleted because they have ${activeReservationsCount} active reservations.`);
        }
        try {
            return await this.prisma.customer.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), blacklisted: true, marketingConsent: false, smsConsent: false, emailConsent: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Customer with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.CustomerService = CustomerService;
exports.CustomerService = CustomerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CustomerService);
//# sourceMappingURL=customer.service.js.map