declare enum ProductUnit {
    PIECE = "PIECE",
    KG = "KG",
    GRAM = "GRAM",
    LITER = "LITER",
    ML = "ML"
}
export declare class CreateRecipeItemDto {
    inventoryItemId: string;
    quantity: number;
    unit: ProductUnit;
    wastagePercent?: number;
}
export declare class CreateRecipeDto {
    productId: string;
    name: string;
    yield: number;
    preparationSteps?: string;
    preparationTime?: number;
    active?: boolean;
    items: CreateRecipeItemDto[];
}
export {};
