"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineProductMappingModule = void 0;
const common_1 = require("@nestjs/common");
const online_product_mapping_service_1 = require("./online-product-mapping.service");
const online_product_mapping_controller_1 = require("./online-product-mapping.controller");
const prisma_module_1 = require("../prisma/prisma.module");
let OnlineProductMappingModule = class OnlineProductMappingModule {
};
exports.OnlineProductMappingModule = OnlineProductMappingModule;
exports.OnlineProductMappingModule = OnlineProductMappingModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [online_product_mapping_controller_1.OnlineProductMappingController],
        providers: [online_product_mapping_service_1.OnlineProductMappingService],
        exports: [online_product_mapping_service_1.OnlineProductMappingService],
    })
], OnlineProductMappingModule);
//# sourceMappingURL=online-product-mapping.module.js.map