"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyTransactionController = void 0;
const common_1 = require("@nestjs/common");
const loyalty_transaction_service_1 = require("./loyalty-transaction.service");
const create_loyalty_transaction_dto_1 = require("./dto/create-loyalty-transaction.dto");
const update_loyalty_transaction_dto_1 = require("./dto/update-loyalty-transaction.dto");
const prisma_1 = require("../../generated/prisma");
const parse_optional_date_pipe_1 = require("../common/pipes/parse-optional-date.pipe");
const parse_optional_enum_pipe_1 = require("../common/pipes/parse-optional-enum.pipe");
let LoyaltyTransactionController = class LoyaltyTransactionController {
    loyaltyTransactionService;
    constructor(loyaltyTransactionService) {
        this.loyaltyTransactionService = loyaltyTransactionService;
    }
    create(createLoyaltyTransactionDto) {
        return this.loyaltyTransactionService.createLoyaltyTransaction(createLoyaltyTransactionDto);
    }
    findAll(cardId, orderId, type, createdBy, startDate, endDate) {
        return this.loyaltyTransactionService.findAllLoyaltyTransactions(cardId, orderId, type, createdBy, startDate, endDate);
    }
    findOne(id) {
        return this.loyaltyTransactionService.findOneLoyaltyTransaction(id);
    }
    update(id, updateLoyaltyTransactionDto) {
        return this.loyaltyTransactionService.updateLoyaltyTransaction(id, updateLoyaltyTransactionDto);
    }
    remove(id) {
        return this.loyaltyTransactionService.removeLoyaltyTransaction(id);
    }
};
exports.LoyaltyTransactionController = LoyaltyTransactionController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_loyalty_transaction_dto_1.CreateLoyaltyTransactionDto]),
    __metadata("design:returntype", void 0)
], LoyaltyTransactionController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('cardId')),
    __param(1, (0, common_1.Query)('orderId')),
    __param(2, (0, common_1.Query)('type', new parse_optional_enum_pipe_1.ParseOptionalEnumPipe(prisma_1.LoyaltyTransactionType))),
    __param(3, (0, common_1.Query)('createdBy')),
    __param(4, (0, common_1.Query)('startDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __param(5, (0, common_1.Query)('endDate', parse_optional_date_pipe_1.ParseOptionalDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Date,
        Date]),
    __metadata("design:returntype", void 0)
], LoyaltyTransactionController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyTransactionController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_loyalty_transaction_dto_1.UpdateLoyaltyTransactionDto]),
    __metadata("design:returntype", void 0)
], LoyaltyTransactionController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoyaltyTransactionController.prototype, "remove", null);
exports.LoyaltyTransactionController = LoyaltyTransactionController = __decorate([
    (0, common_1.Controller)('loyalty-transaction'),
    __metadata("design:paramtypes", [loyalty_transaction_service_1.LoyaltyTransactionService])
], LoyaltyTransactionController);
//# sourceMappingURL=loyalty-transaction.controller.js.map