import { HTMLProps } from '../factory';
export interface TreeViewNodeCheckboxIndicatorBaseProps {
    children?: React.ReactNode | undefined;
    indeterminate?: React.ReactNode | undefined;
    fallback?: React.ReactNode | undefined;
}
export interface TreeViewNodeCheckboxIndicatorProps extends HTMLProps<'span'>, TreeViewNodeCheckboxIndicatorBaseProps {
}
export declare const TreeViewNodeCheckboxIndicator: {
    (props: TreeViewNodeCheckboxIndicatorProps): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
