"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyTransactionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
const loyalty_card_service_1 = require("../loyalty-card/loyalty-card.service");
let LoyaltyTransactionService = class LoyaltyTransactionService {
    prisma;
    loyaltyCardService;
    constructor(prisma, loyaltyCardService) {
        this.prisma = prisma;
        this.loyaltyCardService = loyaltyCardService;
    }
    async createLoyaltyTransaction(data) {
        const loyaltyCard = await this.prisma.loyaltyCard.findUnique({
            where: { id: data.cardId, active: true },
        });
        if (!loyaltyCard) {
            throw new common_1.NotFoundException(`Loyalty card with ID "${data.cardId}" not found or not active.`);
        }
        if (data.orderId) {
            const orderExists = await this.prisma.order.findUnique({
                where: { id: data.orderId, deletedAt: null },
            });
            if (!orderExists) {
                throw new common_1.NotFoundException(`Order with ID "${data.orderId}" not found.`);
            }
        }
        if (data.createdBy) {
            const createdByUser = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
            if (!createdByUser) {
                throw new common_1.NotFoundException(`User with ID "${data.createdBy}" not found.`);
            }
        }
        let newPointBalance = loyaltyCard.points;
        let newMoneyBalance = parseFloat(loyaltyCard.balance.toString());
        switch (data.type) {
            case prisma_1.LoyaltyTransactionType.EARN_PURCHASE:
            case prisma_1.LoyaltyTransactionType.EARN_BONUS:
            case prisma_1.LoyaltyTransactionType.EARN_CAMPAIGN:
            case prisma_1.LoyaltyTransactionType.EARN_BIRTHDAY:
            case prisma_1.LoyaltyTransactionType.EARN_REFERRAL:
            case prisma_1.LoyaltyTransactionType.TRANSFER_IN:
                if (data.points === undefined || data.points <= 0) {
                    throw new common_1.BadRequestException('Points must be a positive integer for this transaction type.');
                }
                await this.loyaltyCardService.addPoints(data.cardId, data.points);
                newPointBalance += data.points;
                break;
            case prisma_1.LoyaltyTransactionType.SPEND_DISCOUNT:
            case prisma_1.LoyaltyTransactionType.SPEND_PRODUCT:
            case prisma_1.LoyaltyTransactionType.TRANSFER_OUT:
            case prisma_1.LoyaltyTransactionType.EXPIRE:
                if (data.points === undefined || data.points <= 0) {
                    throw new common_1.BadRequestException('Points must be a positive integer for this transaction type.');
                }
                await this.loyaltyCardService.spendPoints(data.cardId, data.points);
                newPointBalance -= data.points;
                break;
            case prisma_1.LoyaltyTransactionType.LOAD_BALANCE:
                if (data.amount === undefined || data.amount <= 0) {
                    throw new common_1.BadRequestException('Amount must be a positive number for this transaction type.');
                }
                await this.loyaltyCardService.addBalance(data.cardId, data.amount);
                newMoneyBalance += data.amount;
                break;
            case prisma_1.LoyaltyTransactionType.USE_BALANCE:
                if (data.amount === undefined || data.amount <= 0) {
                    throw new common_1.BadRequestException('Amount must be a positive number for this transaction type.');
                }
                await this.loyaltyCardService.spendBalance(data.cardId, data.amount);
                newMoneyBalance -= data.amount;
                break;
            case prisma_1.LoyaltyTransactionType.ADJUSTMENT:
                if (data.points === undefined && data.amount === undefined) {
                    throw new common_1.BadRequestException('Either points or amount must be provided for ADJUSTMENT transaction type.');
                }
                if (data.points !== undefined) {
                    if (data.points > 0)
                        await this.loyaltyCardService.addPoints(data.cardId, data.points);
                    else if (data.points < 0)
                        await this.loyaltyCardService.spendPoints(data.cardId, Math.abs(data.points));
                    newPointBalance += data.points;
                }
                if (data.amount !== undefined) {
                    if (data.amount > 0)
                        await this.loyaltyCardService.addBalance(data.cardId, data.amount);
                    else if (data.amount < 0)
                        await this.loyaltyCardService.spendBalance(data.cardId, Math.abs(data.amount));
                    newMoneyBalance += data.amount;
                }
                break;
            default:
                throw new common_1.BadRequestException(`Unsupported loyalty transaction type: ${data.type}`);
        }
        const transaction = await this.prisma.loyaltyTransaction.create({
            data: {
                ...data,
                points: data.points !== undefined ? data.points : 0,
                amount: data.amount !== undefined ? parseFloat(data.amount.toFixed(2)) : undefined,
                pointBalance: newPointBalance,
                moneyBalance: newMoneyBalance,
                baseAmount: data.baseAmount !== undefined ? parseFloat(data.baseAmount.toFixed(2)) : undefined,
                multiplier: data.multiplier !== undefined ? parseFloat(data.multiplier.toFixed(2)) : undefined,
                expiresAt: data.expiresAt || undefined,
                createdBy: data.createdBy,
            },
            include: { card: true, order: true },
        });
        return transaction;
    }
    async findAllLoyaltyTransactions(cardId, orderId, type, createdBy, startDate, endDate) {
        return this.prisma.loyaltyTransaction.findMany({
            where: {
                cardId: cardId || undefined,
                orderId: orderId || undefined,
                type: type || undefined,
                createdBy: createdBy || undefined,
                createdAt: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
            },
            include: {
                card: { select: { id: true, cardNumber: true, customerId: true, cardType: true } },
                order: { select: { id: true, orderNumber: true, totalAmount: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneLoyaltyTransaction(id) {
        const transaction = await this.prisma.loyaltyTransaction.findUnique({
            where: { id },
            include: {
                card: { select: { id: true, cardNumber: true, customerId: true, cardType: true } },
                order: { select: { id: true, orderNumber: true, totalAmount: true } },
            },
        });
        if (!transaction) {
            throw new common_1.NotFoundException(`Loyalty transaction with ID "${id}" not found.`);
        }
        return transaction;
    }
    async updateLoyaltyTransaction(id, data) {
        const existingTransaction = await this.findOneLoyaltyTransaction(id);
        if (data.cardId !== undefined || data.orderId !== undefined || data.type !== undefined ||
            data.points !== undefined || data.amount !== undefined || data.baseAmount !== undefined ||
            data.multiplier !== undefined) {
            throw new common_1.ForbiddenException('Cannot update sensitive fields of a loyalty transaction. Only description, expiresAt, and createdBy (if applicable) can be updated.');
        }
        try {
            return await this.prisma.loyaltyTransaction.update({
                where: { id },
                data: {
                    description: data.description,
                    expiresAt: data.expiresAt || undefined,
                    createdBy: data.createdBy,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Loyalty transaction with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeLoyaltyTransaction(id) {
        try {
            const transaction = await this.prisma.loyaltyTransaction.delete({
                where: { id },
            });
            return transaction;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Loyalty transaction with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.LoyaltyTransactionService = LoyaltyTransactionService;
exports.LoyaltyTransactionService = LoyaltyTransactionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        loyalty_card_service_1.LoyaltyCardService])
], LoyaltyTransactionService);
//# sourceMappingURL=loyalty-transaction.service.js.map