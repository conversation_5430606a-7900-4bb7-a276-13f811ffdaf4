import { PrismaService } from '../prisma/prisma.service';
import { CreateOnlinePlatformDto } from './dto/create-online-platform.dto';
import { UpdateOnlinePlatformDto } from './dto/update-online-platform.dto';
export declare class OnlinePlatformService {
    private prisma;
    constructor(prisma: PrismaService);
    createOnlinePlatform(data: CreateOnlinePlatformDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        lastSyncAt: Date | null;
        apiUrl: string | null;
        apiKey: string | null;
        apiSecret: string | null;
        merchantId: string | null;
        storeId: string | null;
        autoAccept: boolean;
        autoReject: number | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        commissionType: string;
        syncProducts: boolean;
        syncInterval: number | null;
        workingHours: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    findAllOnlinePlatforms(companyId?: string): Promise<({
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        lastSyncAt: Date | null;
        apiUrl: string | null;
        apiKey: string | null;
        apiSecret: string | null;
        merchantId: string | null;
        storeId: string | null;
        autoAccept: boolean;
        autoReject: number | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        commissionType: string;
        syncProducts: boolean;
        syncInterval: number | null;
        workingHours: import("generated/prisma/runtime/library").JsonValue | null;
    })[]>;
    findOneOnlinePlatform(id: string): Promise<{
        company: {
            name: string;
            id: string;
        };
    } & {
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        lastSyncAt: Date | null;
        apiUrl: string | null;
        apiKey: string | null;
        apiSecret: string | null;
        merchantId: string | null;
        storeId: string | null;
        autoAccept: boolean;
        autoReject: number | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        commissionType: string;
        syncProducts: boolean;
        syncInterval: number | null;
        workingHours: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    updateOnlinePlatform(id: string, data: UpdateOnlinePlatformDto): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        lastSyncAt: Date | null;
        apiUrl: string | null;
        apiKey: string | null;
        apiSecret: string | null;
        merchantId: string | null;
        storeId: string | null;
        autoAccept: boolean;
        autoReject: number | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        commissionType: string;
        syncProducts: boolean;
        syncInterval: number | null;
        workingHours: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
    removeOnlinePlatform(id: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        lastSyncAt: Date | null;
        apiUrl: string | null;
        apiKey: string | null;
        apiSecret: string | null;
        merchantId: string | null;
        storeId: string | null;
        autoAccept: boolean;
        autoReject: number | null;
        commissionRate: import("generated/prisma/runtime/library").Decimal;
        commissionType: string;
        syncProducts: boolean;
        syncInterval: number | null;
        workingHours: import("generated/prisma/runtime/library").JsonValue | null;
    }>;
}
