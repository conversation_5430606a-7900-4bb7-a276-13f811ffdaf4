// atropos-frontend-desktop/src/main.tsx
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { ChakraProvider } from '@chakra-ui/react'; // ChakraProvider'ı import et
import './index.css';
import App from './App.tsx';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    {/* Uygulamayı ChakraProvider ile sarmala */}
    <ChakraProvider>
      <App />
    </ChakraProvider>
  </StrictMode>,
);
