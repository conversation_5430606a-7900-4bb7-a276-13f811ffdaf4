import { PrismaService } from '../prisma/prisma.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
export declare class BranchService {
    private prisma;
    constructor(prisma: PrismaService);
    createBranch(data: CreateBranchDto): Promise<{
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
    findAllBranches(companyId?: string): Promise<({
        company: {
            name: string;
            taxNumber: string;
            taxOffice: string;
            address: string;
            phone: string;
            email: string;
            logo: string | null;
            website: string | null;
            eArchiveUsername: string | null;
            eArchivePassword: string | null;
            eInvoiceUsername: string | null;
            eInvoicePassword: string | null;
            smsProvider: string | null;
            smsApiKey: string | null;
            smsApiSecret: string | null;
            smsSenderName: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    })[]>;
    findOneBranch(id: string): Promise<{
        company: {
            name: string;
            taxNumber: string;
            taxOffice: string;
            address: string;
            phone: string;
            email: string;
            logo: string | null;
            website: string | null;
            eArchiveUsername: string | null;
            eArchivePassword: string | null;
            eInvoiceUsername: string | null;
            eInvoicePassword: string | null;
            smsProvider: string | null;
            smsApiKey: string | null;
            smsApiSecret: string | null;
            smsSenderName: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
    updateBranch(id: string, data: UpdateBranchDto): Promise<{
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
    removeBranch(id: string): Promise<{
        name: string;
        address: string;
        phone: string;
        email: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        code: string;
        latitude: number | null;
        longitude: number | null;
        serverIp: string | null;
        serverPort: number | null;
        isMainBranch: boolean;
        openingTime: string | null;
        closingTime: string | null;
        workingDays: number[];
        cashRegisterId: string | null;
        posTerminalId: string | null;
        active: boolean;
    }>;
}
