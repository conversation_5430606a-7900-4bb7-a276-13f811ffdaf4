import { CreateInvoiceDto } from './create-invoice.dto';
declare const EArchiveStatus: {
    readonly PENDING: "PENDING";
    readonly SENT: "SENT";
    readonly APPROVED: "APPROVED";
    readonly REJECTED: "REJECTED";
    readonly CANCELLED: "CANCELLED";
};
type EArchiveStatus = typeof EArchiveStatus[keyof typeof EArchiveStatus];
declare const UpdateInvoiceDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateInvoiceDto>>;
export declare class UpdateInvoiceDto extends UpdateInvoiceDto_base {
    serialNo?: string;
    sequenceNo?: string;
    subtotal?: number;
    discountAmount?: number;
    taxAmount?: number;
    totalAmount?: number;
    printedAt?: Date;
    sentAt?: Date;
    viewedAt?: Date;
    isCancelled?: boolean;
    cancelReason?: string;
    cancelledInvoiceId?: string;
    eArchiveStatus?: EArchiveStatus;
}
export {};
