"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethodService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let PaymentMethodService = class PaymentMethodService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createPaymentMethod(data) {
        const companyExists = await this.prisma.company.findUnique({
            where: { id: data.companyId, deletedAt: null },
        });
        if (!companyExists) {
            throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
        const existingPaymentMethod = await this.prisma.paymentMethod.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingPaymentMethod) {
            throw new common_1.ConflictException(`Payment method with code "${data.code}" already exists for this company.`);
        }
        return this.prisma.paymentMethod.create({
            data: {
                ...data,
                commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
                minAmount: data.minAmount !== undefined ? parseFloat(data.minAmount.toFixed(2)) : undefined,
                maxAmount: data.maxAmount !== undefined ? parseFloat(data.maxAmount.toFixed(2)) : undefined,
            },
        });
    }
    async findAllPaymentMethods(companyId) {
        return this.prisma.paymentMethod.findMany({
            where: { companyId: companyId || undefined, deletedAt: null },
            include: { company: { select: { id: true, name: true } } },
            orderBy: { displayOrder: 'asc' },
        });
    }
    async findOnePaymentMethod(id) {
        const paymentMethod = await this.prisma.paymentMethod.findUnique({
            where: { id, deletedAt: null },
            include: { company: { select: { id: true, name: true } } },
        });
        if (!paymentMethod) {
            throw new common_1.NotFoundException(`Payment method with ID "${id}" not found.`);
        }
        return paymentMethod;
    }
    async updatePaymentMethod(id, data) {
        if (data.companyId) {
            const companyExists = await this.prisma.company.findUnique({
                where: { id: data.companyId, deletedAt: null },
            });
            if (!companyExists) {
                throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
            }
        }
        if (data.code) {
            const currentPaymentMethod = await this.findOnePaymentMethod(id);
            const existingPaymentMethod = await this.prisma.paymentMethod.findUnique({
                where: {
                    companyId_code: {
                        companyId: data.companyId || currentPaymentMethod.companyId,
                        code: data.code,
                    },
                },
            });
            if (existingPaymentMethod && existingPaymentMethod.id !== id) {
                throw new common_1.ConflictException(`Payment method with code "${data.code}" already exists for this company.`);
            }
        }
        try {
            return await this.prisma.paymentMethod.update({
                where: { id, deletedAt: null },
                data: {
                    ...data,
                    commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
                    minAmount: data.minAmount !== undefined ? parseFloat(data.minAmount.toFixed(2)) : undefined,
                    maxAmount: data.maxAmount !== undefined ? parseFloat(data.maxAmount.toFixed(2)) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Payment method with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removePaymentMethod(id) {
        const paymentsCount = await this.prisma.payment.count({
            where: { paymentMethodId: id, deletedAt: null }
        });
        if (paymentsCount > 0) {
            throw new common_1.ConflictException(`Payment method with ID "${id}" cannot be deleted because it has ${paymentsCount} associated payments.`);
        }
        try {
            return await this.prisma.paymentMethod.update({
                where: { id, deletedAt: null },
                data: { deletedAt: new Date(), active: false },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Payment method with ID "${id}" not found or already deleted.`);
            }
            throw error;
        }
    }
};
exports.PaymentMethodService = PaymentMethodService;
exports.PaymentMethodService = PaymentMethodService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PaymentMethodService);
//# sourceMappingURL=payment-method.service.js.map