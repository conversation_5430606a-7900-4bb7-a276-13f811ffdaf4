{"mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;;;AAcM,SAAS,0CAAe,IAAwB;IACrD,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,0CAAe;QAC5B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,0CAAe;QAC5B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,mDAAwB;QACrC,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,wCAAa;QAC1B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,wCAAa;QAC1B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,wCAAa;QAC1B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,8CAAmB;QAChC,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,gDAAqB;QAClC,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,iDAAsB;QACnC,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,0CAAe;QAC5B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,yCAAc;QAC3B,KAAK;YACH,OAAO,IAAI,CAAA,GAAA,wCAAa;QAC1B,KAAK;QACL;YACE,OAAO,IAAI,CAAA,GAAA,2CAAgB;IAC/B;AACF", "sources": ["packages/@internationalized/date/src/createCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BuddhistCalendar} from './calendars/BuddhistCalendar';\nimport {Calendar, CalendarIdentifier} from './types';\nimport {CopticCalendar, EthiopicAmeteAlemCalendar, EthiopicCalendar} from './calendars/EthiopicCalendar';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {HebrewCalendar} from './calendars/HebrewCalendar';\nimport {IndianCalendar} from './calendars/IndianCalendar';\nimport {IslamicCivilCalendar, IslamicTabularCalendar, IslamicUmalquraCalendar} from './calendars/IslamicCalendar';\nimport {JapaneseCalendar} from './calendars/JapaneseCalendar';\nimport {PersianCalendar} from './calendars/PersianCalendar';\nimport {TaiwanCalendar} from './calendars/TaiwanCalendar';\n\n/** Creates a `Calendar` instance from a Unicode calendar identifier string. */\nexport function createCalendar(name: CalendarIdentifier): Calendar {\n  switch (name) {\n    case 'buddhist':\n      return new BuddhistCalendar();\n    case 'ethiopic':\n      return new EthiopicCalendar();\n    case 'ethioaa':\n      return new EthiopicAmeteAlemCalendar();\n    case 'coptic':\n      return new CopticCalendar();\n    case 'hebrew':\n      return new HebrewCalendar();\n    case 'indian':\n      return new IndianCalendar();\n    case 'islamic-civil':\n      return new IslamicCivilCalendar();\n    case 'islamic-tbla':\n      return new IslamicTabularCalendar();\n    case 'islamic-umalqura':\n      return new IslamicUmalquraCalendar();\n    case 'japanese':\n      return new JapaneseCalendar();\n    case 'persian':\n      return new PersianCalendar();\n    case 'roc':\n      return new TaiwanCalendar();\n    case 'gregory':\n    default:\n      return new GregorianCalendar();\n  }\n}\n"], "names": [], "version": 3, "file": "createCalendar.main.js.map"}