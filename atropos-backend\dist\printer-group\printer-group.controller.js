"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrinterGroupController = void 0;
const common_1 = require("@nestjs/common");
const printer_group_service_1 = require("./printer-group.service");
const create_printer_group_dto_1 = require("./dto/create-printer-group.dto");
const update_printer_group_dto_1 = require("./dto/update-printer-group.dto");
let PrinterGroupController = class PrinterGroupController {
    printerGroupService;
    constructor(printerGroupService) {
        this.printerGroupService = printerGroupService;
    }
    create(createPrinterGroupDto) {
        return this.printerGroupService.createPrinterGroup(createPrinterGroupDto);
    }
    findAll() {
        return this.printerGroupService.findAllPrinterGroups();
    }
    findOne(id) {
        return this.printerGroupService.findOnePrinterGroup(id);
    }
    update(id, updatePrinterGroupDto) {
        return this.printerGroupService.updatePrinterGroup(id, updatePrinterGroupDto);
    }
    remove(id) {
        return this.printerGroupService.removePrinterGroup(id);
    }
};
exports.PrinterGroupController = PrinterGroupController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_printer_group_dto_1.CreatePrinterGroupDto]),
    __metadata("design:returntype", void 0)
], PrinterGroupController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PrinterGroupController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PrinterGroupController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_printer_group_dto_1.UpdatePrinterGroupDto]),
    __metadata("design:returntype", void 0)
], PrinterGroupController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PrinterGroupController.prototype, "remove", null);
exports.PrinterGroupController = PrinterGroupController = __decorate([
    (0, common_1.Controller)('printer-group'),
    __metadata("design:paramtypes", [printer_group_service_1.PrinterGroupService])
], PrinterGroupController);
//# sourceMappingURL=printer-group.controller.js.map