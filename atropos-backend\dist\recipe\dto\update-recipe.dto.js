"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateRecipeDto = exports.UpdateRecipeItemDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var ProductUnit;
(function (ProductUnit) {
    ProductUnit["PIECE"] = "PIECE";
    ProductUnit["KG"] = "KG";
    ProductUnit["GRAM"] = "GRAM";
    ProductUnit["LITER"] = "LITER";
    ProductUnit["ML"] = "ML";
})(ProductUnit || (ProductUnit = {}));
class UpdateRecipeItemDto {
    id;
    inventoryItemId;
    quantity;
    unit;
    wastagePercent;
}
exports.UpdateRecipeItemDto = UpdateRecipeItemDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateRecipeItemDto.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateRecipeItemDto.prototype, "inventoryItemId", void 0);
__decorate([
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 3 }),
    (0, class_validator_1.Min)(0.001),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateRecipeItemDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(ProductUnit),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateRecipeItemDto.prototype, "unit", void 0);
__decorate([
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateRecipeItemDto.prototype, "wastagePercent", void 0);
class UpdateRecipeDto {
    productId;
    name;
    yield;
    preparationSteps;
    preparationTime;
    active;
    items;
}
exports.UpdateRecipeDto = UpdateRecipeDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateRecipeDto.prototype, "productId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateRecipeDto.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 3 }),
    (0, class_validator_1.Min)(0.001),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateRecipeDto.prototype, "yield", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateRecipeDto.prototype, "preparationSteps", void 0);
__decorate([
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateRecipeDto.prototype, "preparationTime", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateRecipeDto.prototype, "active", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => UpdateRecipeItemDto),
    __metadata("design:type", Array)
], UpdateRecipeDto.prototype, "items", void 0);
//# sourceMappingURL=update-recipe.dto.js.map